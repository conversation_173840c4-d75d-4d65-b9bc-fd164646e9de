<!DOCTYPE html>
<html>

<head>
  <title>
    <%= productName
        %>
  </title>

  <meta charset="utf-8">
  <meta name="description"
        content="<%= productDescription %>">
  <meta name="format-detection"
        content="telephone=no">
  <meta name="msapplication-tap-highlight"
        content="no">
  <meta name="viewport"
        content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width<% if (ctx.mode.cordova || ctx.mode.capacitor) { %>, viewport-fit=cover<% } %>">

  <link rel="icon"
        type="image/png"
        sizes="128x128"
        href="icons/favicon-128x128.png">
  <link rel="icon"
        type="image/png"
        sizes="96x96"
        href="icons/favicon-96x96.png">
  <link rel="icon"
        type="image/png"
        sizes="32x32"
        href="icons/favicon-32x32.png">
  <link rel="icon"
        type="image/png"
        sizes="16x16"
        href="icons/favicon-16x16.png">
  <link rel="icon"
        type="image/ico"
        href="favicon.ico">
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }

    window.addEventListener('cookieConsentAccepted', function () {
      // Load Google Analytics only if consent is given
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=<%= process.env.G_TAG %>';
      document.head.appendChild(script);

      script.onload = function () {
        gtag('js', new Date());
        gtag('config', '<%= process.env.G_TAG %>');
      };
    });

    window.addEventListener('cookieConsentDeclined', function () {
      // Optionally, disable Google Analytics if it was already loaded
      // This might involve clearing cookies or setting opt-out flags
      // For now, we just prevent it from loading if not already loaded
    });
  </script>
  <!-- bing validation below 4 housepriceguess.com -->
  <meta name="msvalidate.01"
        content="C3BB1D13A544DC5FDE26074DCF649469" />
</head>

<body>
  <!-- quasar:entry-point -->
</body>

</html>