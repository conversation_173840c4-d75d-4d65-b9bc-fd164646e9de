<template>
  <q-dialog v-model="showDialog" persistent position="bottom">
    <q-card class="q-pa-md q-ma-sm" style="width: 100%; max-width: 600px;">
      <q-card-section class="row items-center no-wrap">
        <div class="text-h6"><PERSON><PERSON>sent</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup @click="declineCookies" />
      </q-card-section>

      <q-card-section class="q-pt-none">
        <p>We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies. You can manage your preferences by clicking "Decline".</p>
        <p>For more information, please read our <router-link to="/privacy-policy">Privacy Policy</router-link>.</p>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Decline" color="primary" @click="declineCookies" />
        <q-btn unelevated label="Accept All" color="primary" @click="acceptCookies" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';

export default defineComponent({
  name: 'CookieConsent',
  setup() {
    const $q = useQuasar();
    const showDialog = ref(false);
    const COOKIE_CONSENT_KEY = 'cookie_consent';

    const getCookieConsent = () => {
      return $q.cookies.get(COOKIE_CONSENT_KEY);
    };

    const setCookieConsent = (value) => {
      $q.cookies.set(COOKIE_CONSENT_KEY, value, { expires: 365 });
    };

    const acceptCookies = () => {
      setCookieConsent('accepted');
      showDialog.value = false;
      // Trigger Google Analytics initialization
      window.dispatchEvent(new Event('cookieConsentAccepted'));
    };

    const declineCookies = () => {
      setCookieConsent('declined');
      showDialog.value = false;
      // Optionally, disable Google Analytics if it was already loaded
      window.dispatchEvent(new Event('cookieConsentDeclined'));
    };

    onMounted(() => {
      if (!getCookieConsent()) {
        showDialog.value = true;
      } else if (getCookieConsent() === 'accepted') {
        // If already accepted, ensure GA is initialized
        window.dispatchEvent(new Event('cookieConsentAccepted'));
      }
    });

    return {
      showDialog,
      acceptCookies,
      declineCookies,
    };
  },
});
</script>

<style scoped>
/* Add any specific styles for your cookie consent banner here */
</style>
