<template>
  <div class="cc-attribution"
       :class="{ 'cc-attribution-compact': compact }">
    <div class="cc-content">
      <q-icon name="info_outline"
              size="sm"
              class="cc-icon" />
      <span class="cc-text">
        <slot name="prefix">{{ defaultPrefix }}</slot>
        <a :href="licenseUrl"
           target="_blank"
           class="cc-link">
          {{ licenseName }}
        </a>
        <slot name="suffix">{{ defaultSuffix }}</slot>
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  licenseType: {
    type: String,
    default: 'by-2.5',
    validator: (value) => ['by-2.5', 'by-3.0', 'by-4.0', 'by-sa-2.5', 'by-sa-3.0', 'by-sa-4.0'].includes(value)
  },
  source: {
    type: String,
    default: 'Wikipedia'
  },
  compact: {
    type: Boolean,
    default: false
  },
  customPrefix: {
    type: String,
    default: ''
  },
  customSuffix: {
    type: String,
    default: ''
  }
})

const licenseUrl = "https://creativecommons.org/licenses/by/2.5/deed.en"
// Computed properties
// const licenseUrl = computed(() => {
//   const baseUrl = 'https://creativecommons.org/licenses/'
//   return `${baseUrl}${props.licenseType}/`
//   // return "https://creativecommons.org/licenses/by/2.5/deed.en"
// })

const licenseName = computed(() => {
  const licenseNames = {
    'by-2.5': 'Creative Commons Attribution 2.5 Generic License',
    'by-3.0': 'Creative Commons Attribution 3.0 Unported License',
    'by-4.0': 'Creative Commons Attribution 4.0 International License',
    'by-sa-2.5': 'Creative Commons Attribution-ShareAlike 2.5 Generic License',
    'by-sa-3.0': 'Creative Commons Attribution-ShareAlike 3.0 Unported License',
    'by-sa-4.0': 'Creative Commons Attribution-ShareAlike 4.0 International License'
  }
  return licenseNames[props.licenseType] || 'Creative Commons License'
})

const defaultPrefix = computed(() => {
  if (props.customPrefix) return props.customPrefix
  return `Images are sourced from ${props.source} and used under the `
})

const defaultSuffix = computed(() => {
  if (props.customSuffix) return props.customSuffix
  return ''
})
</script>

<style scoped>
.cc-attribution {
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
}

.cc-attribution-compact {
  padding: 8px 16px;
  background-color: transparent;
  border-top: none;
}

.cc-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.cc-icon {
  color: #666;
  flex-shrink: 0;
}

.cc-text {
  font-size: 0.875rem;
  color: #666;
  line-height: 1.4;
}

.cc-link {
  color: var(--q-primary);
  text-decoration: none;
  font-weight: 500;
}

.cc-link:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  .cc-content {
    flex-direction: column;
    gap: 4px;
  }

  .cc-text {
    font-size: 0.8125rem;
    text-align: center;
  }
}
</style>
