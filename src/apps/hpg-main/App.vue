<template>
  <router-view />
</template>
<script>
import { defineComponent } from "vue"
import { websiteProvider } from "src/compose/website-provider.js"
// import { currentSbdUserProvider } from "src/compose/providers/user-from-subdomain-provider.js"
// just commenting out firebase boot file from quasar config
// was not enough to stop calls to this endpoint
// /api_admin/undefined/v1/frb_auth_off
export default defineComponent({
  name: "App",
  provide: {
    websiteProvider,
    // currentSbdUserProvider
  },
})
</script>
