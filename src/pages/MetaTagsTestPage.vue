<template>
  <div class="meta-tags-test-page q-pa-md">
    <h1>Meta Tags Test Page</h1>
    
    <div class="q-mb-lg">
      <h3>Current Meta Tags:</h3>
      <div v-for="(tag, key) in currentMetaTags" :key="key" class="q-mb-sm">
        <strong>{{ key }}:</strong> {{ tag }}
      </div>
    </div>

    <div class="q-mb-lg">
      <h3>Current Route:</h3>
      <p><strong>Name:</strong> {{ $route.name }}</p>
      <p><strong>Path:</strong> {{ $route.path }}</p>
      <p><strong>Params:</strong> {{ JSON.stringify($route.params) }}</p>
    </div>

    <div class="q-mb-lg">
      <h3>Test Links:</h3>
      <div class="q-gutter-sm">
        <q-btn 
          color="primary" 
          label="Game Start Page" 
          @click="$router.push('/game/specials-edition-coventry-house-prices')"
        />
        <q-btn 
          color="secondary" 
          label="Property Page" 
          @click="$router.push('/game/specials-edition-coventry-house-prices/property/e71e4da3-d3fd-439c-8b2c-6cc2fb68b73c?session=test-session-123')"
        />
      </div>
    </div>

    <div>
      <q-btn 
        color="positive" 
        label="Refresh Meta Tags" 
        @click="refreshMetaTags"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'MetaTagsTestPage',
  
  setup() {
    const $route = useRoute()
    const currentMetaTags = ref({})

    const refreshMetaTags = () => {
      const metaTags = {}
      
      // Get title
      metaTags.title = document.title
      
      // Get meta tags
      const metaElements = document.querySelectorAll('meta')
      metaElements.forEach(meta => {
        if (meta.name) {
          metaTags[meta.name] = meta.content
        } else if (meta.property) {
          metaTags[meta.property] = meta.content
        }
      })
      
      currentMetaTags.value = metaTags
    }

    // Watch route changes
    watch(() => $route.path, () => {
      setTimeout(refreshMetaTags, 1000) // Wait for meta tags to update
    })

    onMounted(() => {
      refreshMetaTags()
    })

    return {
      currentMetaTags,
      refreshMetaTags
    }
  }
}
</script>
