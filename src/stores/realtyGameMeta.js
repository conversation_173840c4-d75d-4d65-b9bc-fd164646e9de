import { defineStore } from 'pinia'

export const useRealtyGameMetaStore = defineStore('realtyGameMeta', {
  state: () => ({
    title: 'Property Price Challenge',
    description: 'Test your property market knowledge with our interactive price guessing game.',
    image: 'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
    url: 'https://housepriceguess.com',
    keywords: 'property price game, real estate challenge, property valuation, house price quiz, market knowledge test',
  }),
  actions: {
    setMeta({ title, description, image, url, keywords }) {
      if (title) this.title = title
      if (description) this.description = description
      if (image) this.image = image
      if (url) this.url = url
      if (keywords) this.keywords = keywords
    }
  }
}) 