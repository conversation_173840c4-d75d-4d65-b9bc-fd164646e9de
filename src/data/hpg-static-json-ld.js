// Static JSON-LD structured data for HousePriceGuess/House Price Guess
// This file contains website-level structured data that doesn't change frequently

export const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'HousePriceGuess',
  alternateName: 'House Price Guess',
  description:
    'Make Smarter Real Estate Choices - Advanced property analysis, price guessing games, and comprehensive property intelligence tools.',
  url: 'https://www.housepriceguess.com',
  logo: {
    '@type': 'ImageObject',
    url: 'https://www.housepriceguess.com/icons/favicon-128x128.png',
    width: 128,
    height: 128,
  },
  sameAs: [
    // Add social media profiles when available
  ],
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'customer service',
    availableLanguage: 'English',
  },
  areaServed: {
    '@type': 'Country',
    name: 'United Kingdom',
  },
  knowsAbout: [
    'Real Estate',
    'Property Valuation',
    'Property Analysis',
    'Market Data',
    'Property Intelligence',
  ],
}

export const websiteSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: 'House Price Guess',
  description:
    'Advanced property analysis tools, price guessing games, and comprehensive property intelligence for smarter real estate decisions.',
  url: 'https://www.housepriceguess.com',
  publisher: {
    '@type': 'Organization',
    name: 'HousePriceGuess',
  },
  // potentialAction: {
  //   '@type': 'SearchAction',
  //   target: {
  //     '@type': 'EntryPoint',
  //     urlTemplate:
  //       'https://www.housepriceguess.com/search?q={search_term_string}',
  //   },
  //   'query-input': 'required name=search_term_string',
  // },
}

export const realEstateServiceSchema = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Property Analysis & Intelligence Services',
  description:
    'Comprehensive property analysis including market data, comparables, neighborhood insights, and interactive property price games.',
  provider: {
    '@type': 'Organization',
    name: 'HousePriceGuess',
  },
  serviceType: 'Real Estate Analysis',
  areaServed: {
    '@type': 'Country',
    name: 'United Kingdom',
  },
  hasOfferCatalog: {
    '@type': 'OfferCatalog',
    name: 'Property Services',
    itemListElement: [
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Property Dossier Analysis',
          description:
            'Detailed property analysis including market data, comparables, photos, and neighborhood insights.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Property Price Challenge Game',
          description:
            'Interactive property price guessing game to test and improve property market knowledge.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Neighborhood Analysis',
          description:
            'Curated neighborhood insights with detailed property market analysis and local statistics.',
        },
      },
    ],
  },
}

export const breadcrumbListSchema = (items) => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: items.map((item, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: item.name,
    item: item.url,
  })),
})

// Game-specific schema for property price challenge
export const gameSchema = (gameData) => ({
  '@context': 'https://schema.org',
  '@type': 'Game',
  name: gameData.title || 'Property Price Challenge',
  description:
    gameData.description ||
    'Test your property knowledge with our interactive price guessing game.',
  genre: 'Educational Game',
  gamePlatform: 'Web Browser',
  applicationCategory: 'Game',
  operatingSystem: 'Any',
  offers: {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'GBP',
    availability: 'https://schema.org/InStock',
  },
  publisher: {
    '@type': 'Organization',
    name: 'HousePriceGuess',
  },
})
