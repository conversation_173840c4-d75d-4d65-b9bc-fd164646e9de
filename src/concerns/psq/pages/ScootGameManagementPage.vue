<template>
  <q-page class="scoot-management-page q-pa-md">
    <!-- Loading State -->
    <div v-if="isLoading"
         class="text-center q-pa-xl">
      <q-spinner color="primary"
                 size="3em" />
      <div class="q-mt-md text-grey-7">Loading scoot data...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="text-center q-pa-xl">
      <q-icon name="error_outline"
              color="negative"
              size="3em" />
      <div class="q-mt-md text-negative">{{ error }}</div>
      <q-btn color="primary"
             label="Retry"
             class="q-mt-md"
             @click="loadScootData" />
    </div>

    <!-- Main Content -->
    <div v-else-if="scootData"
         class="scoot-management-content">
      <!-- Page Header -->
      <div class="row items-center q-mb-lg">
        <div class="col">
          <div class="text-h4">Scoot Game Management</div>
          <div class="text-subtitle1 text-grey-7">
            Manage scoot settings and games for this subdomain
          </div>
        </div>
        <div class="col-auto">
          <q-btn color="primary"
                 icon="save"
                 label="Save Changes"
                 @click="saveScootData"
                 :loading="isSaving"
                 :disable="!hasChanges" />
        </div>
      </div>

      <!-- Scoot Data Form -->
      <ScootDataForm v-model="editableScootData"
                     @update:model-value="handleScootDataChange"
                     class="q-mb-lg" />

      <!-- Games Management Section -->
      <q-card flat
              bordered
              class="games-section">
        <q-card-section class="section-header bg-primary text-white">
          <div class="row items-center">
            <q-icon name="games"
                    size="md"
                    class="q-mr-md" />
            <div class="text-h6 text-weight-medium">Available Games</div>
            <q-space />
            <q-btn flat
                   icon="add"
                   label="Add Game"
                   @click="showAddGameDialog = true"
                   class="text-white" />
          </div>
        </q-card-section>

        <q-card-section class="q-pa-lg">
          <div v-if="!editableScootData.available_games_details?.length"
               class="text-center q-pa-lg text-grey-6">
            <q-icon name="games"
                    size="4em"
                    class="q-mb-md" />
            <div class="text-h6">No games configured</div>
            <div class="text-body2">Click "Add Game" to get started</div>
          </div>

          <div v-else
               class="games-list">
            <GameListItem v-for="game in editableScootData.available_games_details"
                          :key="game.uuid"
                          :game="game"
                          @delete="confirmDeleteGame"
                          class="q-mb-md" />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Add Game Dialog -->
    <GameSelectionDialog v-model="showAddGameDialog"
                         :subdomain-name="subdomainName"
                         @game-selected="handleGameSelected" />

    <!-- Delete Game Confirmation -->
    <q-dialog v-model="showDeleteConfirm"
              persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="delete"
                    color="negative"
                    text-color="white" />
          <span class="q-ml-sm">Are you sure you want to remove this game?</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat
                 label="Cancel"
                 color="primary"
                 v-close-popup />
          <q-btn flat
                 label="Delete"
                 color="negative"
                 @click="executeDeleteGame"
                 :loading="isDeleting" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import useScoots from 'src/compose/useScoots'
import ScootDataForm from '../components/ScootDataForm.vue'
import GameListItem from '../components/GameListItem.vue'
import GameSelectionDialog from '../components/GameSelectionDialog.vue'

const $route = useRoute()
const $q = useQuasar()

// Get subdomain name from route or environment
const subdomainName = computed(() => {
  // Extract subdomain from current URL or use a default
  let hostname = "" //
  if (typeof window !== "undefined") {
    hostname = window.location.hostname
  } //window.location.hostname
  const parts = hostname.split('.')
  return parts.length > 2 ? parts[0] : 'hpg-scoot'
})

// Composables
const {
  getScootForRealtyGames,
  updateScootData,
  removeGameFromScoot
} = useScoots()

// State
const scootData = ref(null)
const editableScootData = ref(null)
const isLoading = ref(false)
const isSaving = ref(false)
const isDeleting = ref(false)
const error = ref(null)
const hasChanges = ref(false)
const showAddGameDialog = ref(false)
const showDeleteConfirm = ref(false)
const gameToDelete = ref(null)

// Methods
const loadScootData = async () => {
  isLoading.value = true
  error.value = null

  try {
    const response = await getScootForRealtyGames(subdomainName.value)
    scootData.value = response.data.scoot
    editableScootData.value = JSON.parse(JSON.stringify(response.data.scoot))
    hasChanges.value = false
  } catch (err) {
    error.value = err.response?.data?.message || 'Failed to load scoot data'
    console.error('Error loading scoot data:', err)
  } finally {
    isLoading.value = false
  }
}

const handleScootDataChange = (newData) => {
  editableScootData.value = newData
  hasChanges.value = true
}

const saveScootData = async () => {
  isSaving.value = true

  try {
    await updateScootData(subdomainName.value, editableScootData.value)
    scootData.value = JSON.parse(JSON.stringify(editableScootData.value))
    hasChanges.value = false

    $q.notify({
      type: 'positive',
      message: 'Scoot data saved successfully',
      position: 'top'
    })
  } catch (err) {
    $q.notify({
      type: 'negative',
      message: err.response?.data?.message || 'Failed to save scoot data',
      position: 'top'
    })
  } finally {
    isSaving.value = false
  }
}

const handleGameSelected = (game) => {
  // Add the selected game to the available games list
  if (!editableScootData.value.available_games_details) {
    editableScootData.value.available_games_details = []
  }

  // Check if game already exists
  const exists = editableScootData.value.available_games_details.some(
    existingGame => existingGame.uuid === game.uuid
  )

  if (!exists) {
    editableScootData.value.available_games_details.push(game)
    hasChanges.value = true

    $q.notify({
      type: 'positive',
      message: `Game "${game.game_title}" added successfully`,
      position: 'top'
    })
  } else {
    $q.notify({
      type: 'warning',
      message: 'This game is already in the list',
      position: 'top'
    })
  }
}

const confirmDeleteGame = (game) => {
  gameToDelete.value = game
  showDeleteConfirm.value = true
}

const executeDeleteGame = async () => {
  if (!gameToDelete.value) return

  isDeleting.value = true

  try {
    // Remove from local data
    const index = editableScootData.value.available_games_details.findIndex(
      game => game.uuid === gameToDelete.value.uuid
    )

    if (index > -1) {
      editableScootData.value.available_games_details.splice(index, 1)
      hasChanges.value = true
    }

    // Also call API to remove from backend if needed
    await removeGameFromScoot(subdomainName.value, gameToDelete.value.uuid)

    $q.notify({
      type: 'positive',
      message: 'Game removed successfully',
      position: 'top'
    })

    showDeleteConfirm.value = false
    gameToDelete.value = null
  } catch (err) {
    $q.notify({
      type: 'negative',
      message: err.response?.data?.message || 'Failed to remove game',
      position: 'top'
    })
  } finally {
    isDeleting.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadScootData()
})
</script>

<style scoped>
.scoot-management-page {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  border-radius: 4px 4px 0 0;
}

.games-section {
  border-radius: 8px;
  overflow: hidden;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
