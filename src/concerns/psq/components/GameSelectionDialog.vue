<template>
  <q-dialog :model-value="modelValue"
            @update:model-value="$emit('update:modelValue', $event)"
            persistent
            maximized
            transition-show="slide-up"
            transition-hide="slide-down">
    <q-card class="game-selection-dialog">
      <!-- Header -->
      <q-card-section class="row items-center q-pb-none bg-primary text-white">
        <div class="text-h6">Add Game to Scoot</div>
        <q-space />
        <q-btn icon="close"
               flat
               round
               dense
               v-close-popup
               class="text-white" />
      </q-card-section>

      <!-- Search and Filters -->
      <q-card-section class="q-pb-none">
        <div class="row q-gutter-md items-end">
          <div class="col">
            <q-input filled
                     v-model="searchQuery"
                     label="Search games"
                     placeholder="Search by title, description, or slug..."
                     clearable
                     @update:model-value="filterGames">
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>

          <div class="col-auto">
            <q-btn color="primary"
                   icon="refresh"
                   label="Refresh"
                   @click="loadAvailableGames"
                   :loading="isLoading" />
          </div>
        </div>
      </q-card-section>

      <!-- Loading State -->
      <q-card-section v-if="isLoading"
                      class="text-center q-pa-xl">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-grey-7">Loading available games...</div>
      </q-card-section>

      <!-- Error State -->
      <q-card-section v-else-if="error"
                      class="text-center q-pa-xl">
        <q-icon name="error_outline"
                color="negative"
                size="3em" />
        <div class="q-mt-md text-negative">{{ error }}</div>
        <q-btn color="primary"
               label="Retry"
               class="q-mt-md"
               @click="loadAvailableGames" />
      </q-card-section>

      <!-- Games List -->
      <q-card-section v-else
                      class="games-content">
        <div v-if="!filteredGames.length"
             class="text-center q-pa-xl text-grey-6">
          <q-icon name="games"
                  size="4em"
                  class="q-mb-md" />
          <div class="text-h6">No games found</div>
          <div class="text-body2">
            {{ searchQuery ? 'Try adjusting your search criteria' : 'No games available to add' }}
          </div>
        </div>

        <div v-else
             class="games-grid">
          <q-card v-for="game in filteredGames"
                  :key="game.uuid"
                  flat
                  bordered
                  class="game-card cursor-pointer"
                  @click="selectGame(game)">
            <q-card-section class="q-pa-md">
              <div class="row items-start">
                <div class="col">
                  <div class="text-h6 text-weight-medium q-mb-xs">
                    {{ game.game_title || 'Untitled Game' }}
                  </div>

                  <div class="text-body2 text-grey-7 q-mb-sm">
                    {{ game.game_description || 'No description available' }}
                  </div>

                  <div class="text-caption text-grey-6 q-mb-md">
                    Slug: {{ game.realty_game_slug }}
                  </div>

                  <!-- Game Stats -->
                  <div class="row q-gutter-sm">
                    <q-chip size="sm"
                            outline
                            color="primary"
                            icon="list">
                      {{ game.game_listings_count || 0 }} Properties
                    </q-chip>

                    <q-chip size="sm"
                            outline
                            color="secondary"
                            icon="people">
                      {{ game.game_sessions_count || 0 }} Sessions
                    </q-chip>

                    <q-chip size="sm"
                            outline
                            color="accent"
                            icon="quiz">
                      {{ game.guessed_prices_count || 0 }} Guesses
                    </q-chip>
                  </div>
                </div>

                <div class="col-auto">
                  <q-btn color="primary"
                         icon="add"
                         label="Add"
                         @click.stop="selectGame(game)"
                         size="sm" />
                </div>
              </div>

              <!-- Additional Game Info -->
              <q-separator class="q-my-md" />

              <div class="row q-gutter-md text-caption text-grey-6">
                <div class="col-auto">
                  <strong>Currency:</strong> {{ game.game_default_currency || 'N/A' }}
                </div>
                <div class="col-auto">
                  <strong>Country:</strong> {{ game.game_default_country || 'N/A' }}
                </div>
                <div class="col-auto">
                  <strong>Locale:</strong> {{ game.game_default_locale || 'N/A' }}
                </div>
              </div>

              <div class="row q-gutter-md text-caption text-grey-6 q-mt-xs">
                <div class="col-auto">
                  <strong>Start:</strong> {{ formatDate(game.game_start_at) }}
                </div>
                <div class="col-auto">
                  <strong>End:</strong> {{ formatDate(game.game_end_at) }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import useScoots from 'src/compose/useScoots'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  subdomainName: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'game-selected'])

const $q = useQuasar()

// Composables
const { getManagementGames } = useScoots()

// State
const availableGames = ref([])
const isLoading = ref(false)
const error = ref(null)
const searchQuery = ref('')

// Computed
const filteredGames = computed(() => {
  if (!searchQuery.value) return availableGames.value

  const query = searchQuery.value.toLowerCase()
  return availableGames.value.filter(game =>
    (game.game_title || '').toLowerCase().includes(query) ||
    (game.game_description || '').toLowerCase().includes(query) ||
    (game.realty_game_slug || '').toLowerCase().includes(query)
  )
})

// Methods
const loadAvailableGames = async () => {
  isLoading.value = true
  error.value = null

  try {
    const response = await getManagementGames(props.subdomainName)
    availableGames.value = response.data.available_realty_games || []
  } catch (err) {
    error.value = err.response?.data?.message || 'Failed to load available games'
    console.error('Error loading available games:', err)
  } finally {
    isLoading.value = false
  }
}

const selectGame = (game) => {
  emit('game-selected', game)
  emit('update:modelValue', false)

  $q.notify({
    type: 'positive',
    message: `Selected game: ${game.game_title}`,
    position: 'top'
  })
}

const filterGames = () => {
  // Filtering is handled by computed property
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  } catch (error) {
    return 'Invalid Date'
  }
}

// Watch for dialog opening
watch(() => props.modelValue, (isOpen) => {
  if (isOpen && !availableGames.value.length) {
    loadAvailableGames()
  }
})

// Load games on mount if dialog is already open
onMounted(() => {
  if (props.modelValue) {
    loadAvailableGames()
  }
})
</script>

<style scoped>
.game-selection-dialog {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.games-content {
  flex: 1;
  overflow-y: auto;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.game-card {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.game-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.q-chip {
  font-size: 0.75rem;
}

@media (max-width: 768px) {
  .games-grid {
    grid-template-columns: 1fr;
  }
}
</style>
