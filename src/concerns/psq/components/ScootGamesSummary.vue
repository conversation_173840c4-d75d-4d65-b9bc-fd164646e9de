<template>
  <div class="scoot-games-summary">
    <!-- Header Section -->
    <div class="games-header q-mb-lg">
      <div class="row items-center">
        <!-- <div class="col">
          <h1 class="games-title">Available Games</h1>
          <p class="games-subtitle">Choose from our exciting property price guessing games</p>
        </div>
        <div class="col-auto">
          <div class="header-controls">
            <q-input filled
                     v-model="searchQuery"
                     label="Search games"
                     placeholder="Search by title or description..."
                     clearable
                     class="search-input"
                     @update:model-value="filterGames">
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
</q-input>

<q-btn color="primary" icon="refresh" label="Refresh" @click="loadAvailableGames" :loading="isLoading"
  class="refresh-btn" />
</div>
</div> -->
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading"
         class="loading-state">
      <q-spinner color="primary"
                 size="3em" />
      <div class="q-mt-md text-grey-7">Loading available games...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="error-state">
      <q-icon name="error_outline"
              color="negative"
              size="3em" />
      <div class="q-mt-md text-negative">{{ error }}</div>
      <!-- <q-btn color="primary"
             label="Retry"
             class="q-mt-md"
             @click="loadAvailableGames" /> -->
    </div>

    <!-- Games Display -->
    <div v-else
         class="games-display">
      <!-- No Results for Search -->
      <!-- <div v-if="searchQuery && !filteredGames.length"
           class="no-results">
        <q-icon name="search_off"
                size="4em"
                class="q-mb-md text-grey-5" />
        <div class="text-h6 text-grey-7">No games match your search</div>
        <div class="text-body2 text-grey-6">
          Try adjusting your search criteria or
          <q-btn flat
                 color="primary"
                 @click="searchQuery = ''"
                 class="q-pa-none">
            clear the search
          </q-btn>
        </div>
      </div> -->

      <!-- Games List Component -->
      <GameRowDisplay :games="availableHpgGames"
                      @game-selected="handleGameSelected" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import useScoots from 'src/compose/useScoots'
import GameRowDisplay from './GameRowDisplay.vue'

// Props
const props = defineProps({
  availableHpgGames: {
    type: Array,
    default: () => []
  },
  subdomainName: {
    type: String,
    required: false,
    default: "hpg-scoot"
  },
  autoLoad: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['game-selected', 'games-loaded'])

const $q = useQuasar()

// Composables
const { getManagementGames } = useScoots()

// State
const availableGames = ref([])
const isLoading = ref(false)
const error = ref(null)
// const searchQuery = ref('')

// Computed
// const filteredGames = computed(() => {
//   const games = props.availableRealtyGames.length > 0 ? props.availableRealtyGames : availableGames.value

//   if (!searchQuery.value) return games

//   const query = searchQuery.value.toLowerCase()
//   return games.filter(game =>
//     (game.game_title || '').toLowerCase().includes(query) ||
//     (game.game_description || '').toLowerCase().includes(query) ||
//     (game.realty_game_slug || '').toLowerCase().includes(query)
//   )
// })

// Methods
const loadAvailableGames = async () => {
  isLoading.value = true
  error.value = null

  try {
    const response = await getManagementGames(props.subdomainName)
    const games = response.data?.available_realty_games || []
    availableGames.value = games
    emit('games-loaded', games)
  } catch (err) {
    error.value = err.response?.data?.message || 'Failed to load available games'
    console.error('Error loading available games:', err)
  } finally {
    isLoading.value = false
  }
}

const handleGameSelected = (game) => {
  emit('game-selected', game)

  $q.notify({
    type: 'positive',
    message: `Starting game: ${game.game_title}`,
    position: 'top'
  })
}

const filterGames = () => {
  // Filtering is handled by computed property
}

// // Load games immediately on mount if autoLoad is true and no games provided via props
// onMounted(() => {
//   if (props.autoLoad && props.availableRealtyGames.length === 0) {
//     loadAvailableGames()
//   }
// })
</script>

<style scoped>
.scoot-games-summary {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
}

.games-header {
  text-align: center;
  margin-bottom: 48px;
}

.games-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--q-primary);
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.games-subtitle {
  font-size: 1.3rem;
  color: var(--q-dark);
  margin: 0 0 32px 0;
  opacity: 0.8;
}

.header-controls {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  justify-content: center;
  flex-wrap: wrap;
}

.search-input {
  min-width: 300px;
}

.refresh-btn {
  height: 56px;
  /* Match input height */
}

.loading-state,
.error-state,
.no-results {
  text-align: center;
  padding: 64px 32px;
}

.loading-state {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16px;
}

.error-state {
  background: rgba(244, 67, 54, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(244, 67, 54, 0.1);
}

.no-results {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16px;
}

.games-display {
  width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .scoot-games-summary {
    padding: 0 12px;
  }

  .games-header {
    margin-bottom: 32px;
  }

  .games-title {
    font-size: 2.2rem;
  }

  .games-subtitle {
    font-size: 1.1rem;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
    width: 100%;
  }

  .refresh-btn {
    width: 100%;
    height: 48px;
  }

  .loading-state,
  .error-state,
  .no-results {
    padding: 48px 24px;
  }
}

@media (max-width: 480px) {
  .games-title {
    font-size: 1.8rem;
  }

  .games-subtitle {
    font-size: 1rem;
  }

  .loading-state,
  .error-state,
  .no-results {
    padding: 32px 16px;
  }
}
</style>