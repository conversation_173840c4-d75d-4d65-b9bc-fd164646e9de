<template>
  <q-card flat
          bordered
          class="scoot-data-form">
    <q-card-section class="section-header bg-secondary text-white">
      <div class="row items-center">
        <q-icon name="settings"
                size="md"
                class="q-mr-md" />
        <div class="text-h6 text-weight-medium">Scoot Configuration</div>
      </div>
    </q-card-section>

    <q-card-section class="q-pa-lg">
      <q-form @submit.prevent="onSubmit"
              class="scoot-form">
        <!-- Basic Information Section -->
        <div class="form-section q-mb-lg">
          <div class="section-title q-mb-md">
            <q-icon name="info"
                    class="q-mr-sm" />
            Basic Information
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input filled
                       v-model="editableData.scoot_title"
                       label="Scoot Title"
                       hint="Display title for this scoot"
                       clearable
                       @update:model-value="emitUpdate">
                <template v-slot:prepend>
                  <q-icon name="title" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-md-6">
              <q-input filled
                       v-model="editableData.scoot_notice"
                       label="Scoot Notice"
                       hint="Notice message displayed to users"
                       clearable
                       @update:model-value="emitUpdate">
                <template v-slot:prepend>
                  <q-icon name="announcement" />
                </template>
              </q-input>
            </div>
          </div>
        </div>

        <!-- Game Settings Section -->
        <div class="form-section q-mb-lg">
          <div class="section-title q-mb-md">
            <q-icon name="games"
                    class="q-mr-sm" />
            Game Settings
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-4">
              <q-toggle v-model="editableData.is_price_guess_enabled"
                        label="Enable Price Guessing"
                        color="primary"
                        @update:model-value="emitUpdate" />
            </div>

            <div class="col-12 col-md-4">
              <q-toggle v-model="editableData.supports_multiple_games"
                        label="Support Multiple Games"
                        color="primary"
                        @update:model-value="emitUpdate" />
            </div>

            <div class="col-12 col-md-4">
              <q-toggle v-model="editableData.is_price_guess_only"
                        label="Price Guess Only"
                        color="primary"
                        @update:model-value="emitUpdate" />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-12 col-md-6">
              <q-toggle v-model="editableData.is_price_guess_public"
                        label="Public Price Guessing"
                        color="primary"
                        @update:model-value="emitUpdate" />
            </div>
          </div>
        </div>

        <!-- Advanced Settings Section -->
        <q-expansion-item icon="tune"
                          label="Advanced Settings"
                          default-opened
                          class="advanced-settings">
          <div class="q-pa-md">
            <div class="row q-gutter-md">
              <div class="col-12">
                <q-input filled
                         v-model="rawJsonData"
                         label="Raw JSON Data"
                         type="textarea"
                         rows="10"
                         hint="Edit the complete scoot data as JSON (advanced users only)"
                         @update:model-value="handleRawJsonChange">
                  <template v-slot:prepend>
                    <q-icon name="data_object" />
                  </template>
                </q-input>

                <div v-if="jsonError"
                     class="text-negative q-mt-sm">
                  <q-icon name="error"
                          class="q-mr-xs" />
                  {{ jsonError }}
                </div>
              </div>
            </div>
          </div>
        </q-expansion-item>
      </q-form>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// State
const editableData = ref(JSON.parse(JSON.stringify(props.modelValue)))
const jsonError = ref('')

// Computed
const rawJsonData = computed({
  get() {
    return JSON.stringify(editableData.value, null, 2)
  },
  set(value) {
    // This will be handled by handleRawJsonChange
  }
})

// Methods
const onSubmit = () => {
  // debugger
  // emit('update:modelValue', JSON.parse(JSON.stringify(editableData.value)))
}

const emitUpdate = () => {
  emit('update:modelValue', JSON.parse(JSON.stringify(editableData.value)))
}

const handleRawJsonChange = (newJsonString) => {
  try {
    const parsed = JSON.parse(newJsonString)
    editableData.value = parsed
    jsonError.value = ''
    emitUpdate()
  } catch (error) {
    jsonError.value = 'Invalid JSON format'
  }
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  editableData.value = JSON.parse(JSON.stringify(newValue))
}, { deep: true })
</script>

<style scoped>
.scoot-data-form {
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  border-radius: 4px 4px 0 0;
}

.form-section {
  border-left: 4px solid var(--q-primary);
  padding-left: 16px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--q-primary);
  display: flex;
  align-items: center;
}

.advanced-settings {
  border: 1px solid var(--q-separator-color);
  border-radius: 4px;
  margin-top: 16px;
}

.scoot-form .q-field {
  margin-bottom: 8px;
}

.q-toggle {
  margin-bottom: 8px;
}
</style>
