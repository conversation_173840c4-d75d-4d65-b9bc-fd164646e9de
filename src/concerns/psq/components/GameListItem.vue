<template>
  <q-card flat bordered class="game-list-item">
    <q-card-section class="q-pa-md">
      <div class="row items-center">
        <!-- Game Icon -->
        <div class="col-auto q-mr-md">
          <q-avatar 
            size="48px" 
            :color="game.is_hidden_from_landing_page ? 'grey-5' : 'primary'"
            text-color="white"
            icon="games"
          />
        </div>
        
        <!-- Game Information -->
        <div class="col">
          <div class="row items-center q-mb-xs">
            <div class="text-h6 text-weight-medium">
              {{ game.game_title || 'Untitled Game' }}
            </div>
            <q-chip 
              v-if="game.is_hidden_from_landing_page"
              size="sm"
              color="grey-5"
              text-color="white"
              icon="visibility_off"
              class="q-ml-sm"
            >
              Hidden
            </q-chip>
          </div>
          
          <div class="text-body2 text-grey-7 q-mb-sm">
            {{ game.game_description || 'No description available' }}
          </div>
          
          <!-- Game Stats -->
          <div class="row q-gutter-md">
            <div class="col-auto">
              <q-chip 
                size="sm" 
                outline 
                color="primary"
                icon="list"
              >
                {{ game.game_listings_count || 0 }} Properties
              </q-chip>
            </div>
            
            <div class="col-auto">
              <q-chip 
                size="sm" 
                outline 
                color="secondary"
                icon="people"
              >
                {{ game.game_sessions_count || 0 }} Sessions
              </q-chip>
            </div>
            
            <div class="col-auto">
              <q-chip 
                size="sm" 
                outline 
                color="accent"
                icon="quiz"
              >
                {{ game.guessed_prices_count || 0 }} Guesses
              </q-chip>
            </div>
          </div>
        </div>
        
        <!-- Game Actions -->
        <div class="col-auto">
          <div class="row q-gutter-sm">
            <!-- Game Details Button -->
            <q-btn
              flat
              round
              icon="info"
              color="primary"
              @click="showGameDetails = true"
              size="sm"
            >
              <q-tooltip>View Details</q-tooltip>
            </q-btn>
            
            <!-- Delete Button -->
            <q-btn
              flat
              round
              icon="delete"
              color="negative"
              @click="$emit('delete', game)"
              size="sm"
            >
              <q-tooltip>Remove Game</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
      
      <!-- Game Metadata (Expandable) -->
      <q-expansion-item
        v-if="showMetadata"
        icon="info"
        label="Game Details"
        class="q-mt-md"
      >
        <div class="q-pa-md bg-grey-1">
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">Game Slug</div>
              <div class="text-body2">{{ game.realty_game_slug || 'N/A' }}</div>
            </div>
            
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">UUID</div>
              <div class="text-body2 font-mono">{{ game.uuid }}</div>
            </div>
            
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">Default Currency</div>
              <div class="text-body2">{{ game.game_default_currency || 'N/A' }}</div>
            </div>
            
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">Default Country</div>
              <div class="text-body2">{{ game.game_default_country || 'N/A' }}</div>
            </div>
            
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">Start Date</div>
              <div class="text-body2">
                {{ formatDate(game.game_start_at) }}
              </div>
            </div>
            
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">End Date</div>
              <div class="text-body2">
                {{ formatDate(game.game_end_at) }}
              </div>
            </div>
          </div>
        </div>
      </q-expansion-item>
    </q-card-section>
    
    <!-- Game Details Dialog -->
    <q-dialog v-model="showGameDetails">
      <q-card style="min-width: 400px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ game.game_title }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        
        <q-card-section>
          <div class="text-body2 q-mb-md">
            {{ game.game_description }}
          </div>
          
          <q-list dense>
            <q-item>
              <q-item-section>
                <q-item-label caption>Game Slug</q-item-label>
                <q-item-label>{{ game.realty_game_slug }}</q-item-label>
              </q-item-section>
            </q-item>
            
            <q-item>
              <q-item-section>
                <q-item-label caption>Properties</q-item-label>
                <q-item-label>{{ game.game_listings_count }}</q-item-label>
              </q-item-section>
            </q-item>
            
            <q-item>
              <q-item-section>
                <q-item-label caption>Sessions</q-item-label>
                <q-item-label>{{ game.game_sessions_count }}</q-item-label>
              </q-item-section>
            </q-item>
            
            <q-item>
              <q-item-section>
                <q-item-label caption>Total Guesses</q-item-label>
                <q-item-label>{{ game.guessed_prices_count }}</q-item-label>
              </q-item-section>
            </q-item>
            
            <q-item>
              <q-item-section>
                <q-item-label caption>Default Currency</q-item-label>
                <q-item-label>{{ game.game_default_currency }}</q-item-label>
              </q-item-section>
            </q-item>
            
            <q-item>
              <q-item-section>
                <q-item-label caption>Default Country</q-item-label>
                <q-item-label>{{ game.game_default_country }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-card>
</template>

<script setup>
import { ref } from 'vue'

// Props
const props = defineProps({
  game: {
    type: Object,
    required: true
  },
  showMetadata: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['delete'])

// State
const showGameDetails = ref(false)

// Methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return 'Invalid Date'
  }
}
</script>

<style scoped>
.game-list-item {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.game-list-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.font-mono {
  font-family: 'Courier New', monospace;
  font-size: 0.85em;
}

.q-chip {
  font-size: 0.75rem;
}
</style>
