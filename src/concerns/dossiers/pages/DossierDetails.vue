<template>
  <div v-if="saleListing"
       flat
       bordered
       class="dossier-details-page">

    <!-- JSON-LD for Property -->
    <PropertyJsonLd :property="saleListing"
                    :breadcrumbs="breadcrumbs" />

    <h3 class="text-h5 q-mb-lg q-mt-lg text-center h2c-underline">Details of your Northstar Property</h3>

    <div class="q-px-md">
      <div class="text-h6 col-xs-12 q-pb-sm">{{ saleListing.catchy_title || saleListing.title }}</div>
      <div class="col-xs-12 col-md-6">
        <div class="text-subtitle2">{{ saleListing.street_name }}</div>
        <div class="text-subtitle2">{{ saleListing.street_address }}</div>
        <div class="text-subtitle2">{{ saleListing.postal_code }}</div>
      </div>
    </div>
    <div class="q-px-md">
      <ListingPriceAndRooms :evaluationDetails="saleListing"></ListingPriceAndRooms>
    </div>

    <ListingDetailsBlockMain :showDescription="false"
                             :relevantSoldTransactions="relevantSoldTransactions"
                             :realtyDossier="realtyDossier"
                             :evaluationDetails="saleListing"></ListingDetailsBlockMain>

    <div v-if="realtyDossier?.dossier_starting_url"
         class="row or-ls-url q-py-md">
      <div class="col-xs-12 text-center">
        <span>Original Listing: </span>
        <a target="_blank"
           :href="realtyDossier.dossier_starting_url">
          {{ realtyDossier.dossier_starting_url }}</a>
      </div>
    </div>

    <div v-if="realtyDossier?.dossier_starting_url"
         class="row or-ls-url q-py-md">
      <div class="col-xs-12 text-center">
        <span></span>
        <router-link :to="{
          name: 'rSinglePriceGuessProperty',
          params: { propertyUuid: saleListing.uuid },
        }">
          Click here for a link you can share with friends and family to guess the price of this property.
        </router-link>
      </div>
    </div>

    <div>
    </div>
  </div>
</template>

<script>
import ListingPriceAndRooms from "src/concerns/for-sale-evaluations/components/listing-blocks/ListingPriceAndRooms.vue"
import ListingDetailsBlockMain from "src/concerns/for-sale-evaluations/components/listing-blocks/ListingDetailsBlockMain.vue"
import PropertyJsonLd from "src/components/seo/PropertyJsonLd.vue"
export default {
  name: 'DossierDetails',
  components: {
    ListingPriceAndRooms,
    ListingDetailsBlockMain,
    PropertyJsonLd,
  },
  data() {
    return {}
  },
  computed: {
    breadcrumbs() {
      return [
        { name: 'Home', url: '/' },
        { name: 'Dossiers', url: '/dossiers' },
        { name: this.saleListing?.title || 'Property Details', url: window.location.href }
      ]
    }
  },
  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    relevantSoldTransactions() {
      if (this.realtyDossier?.recent_sales_analysis?.other_recent_sales) {
        return [
          this.realtyDossier?.recent_sales_analysis?.most_similar_recent_sale,
          ...this.realtyDossier?.recent_sales_analysis?.other_recent_sales
        ]
      }
      else {
        return []
      }
    },
    targetChartName() {
      return this.$route.params.targetChartName || 'price_by_floor_area'
    },
  },
  methods: {
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1).replace(/_/g, ' ')
    },
    modalStateChanged(newVal) {
      this.modalOpen = newVal;
    },
    openChartModal(selectedPoint) {
      this.selectedPoint = selectedPoint
      this.modalOpen = true;
      const queryString = new URLSearchParams({ actionId: selectedPoint.SoldTransactionId }).toString();
      window.history.pushState(null, '', `?${queryString}`);
      // window.dispatchEvent(new Event('popstate'))
    },
    passChartSeries(chartSeries) {
      this.soldDataPoints = chartSeries[0].data
      // transactionLatitude = chartSeries[0].data[0].Latitude
    },
  }
};
</script>

<style scoped></style>
