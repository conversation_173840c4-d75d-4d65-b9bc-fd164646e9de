<template>
  <q-footer
    v-if="timeToShowFooter"
    elevated
    class="htoc-gate-foot text-white bg-gradient"
  >
    <q-toolbar>
      <q-toolbar-title>
        <div class="text-center q-pa-sm text-body1">
          <router-link
            class="text-white"
            :to="{ name: 'rInfoPage', params: { page_slug: 'privacy' } }"
          >
            Privacy Policy
          </router-link>

          |

          <router-link
            class="text-white"
            :to="{
              name: 'rInfoPage',
              params: { page_slug: 'terms-of-service' },
            }"
          >
            Terms Of Service
          </router-link>

          <!-- |

          <router-link class="text-white"
                       :to="{
                        name: 'rH2cLoginPage',
                        params: {},
                      }">
            Sign In
          </router-link>

          |

          <router-link class="text-white"
                       :to="{
                        name: 'rCreateAccount',
                        params: {},
                      }">
            Create An Account
          </router-link> -->

          <!-- |

          <router-link
            class="text-white"
            :to="{
              name: 'rInfoPage',
              params: { page_slug: 'for-home-buyers' },
            }"
          >
            For Home Buyers
          </router-link>

          |

          <router-link
            class="text-white"
            :to="{
              name: 'rInfoPage',
              params: { page_slug: 'for-home-sellers' },
            }"
          >
            For Home Sellers
          </router-link>

          |

          <router-link
            class="text-white"
            :to="{
              name: 'rInfoPage',
              params: { page_slug: 'for-estate-agents' },
            }"
          >
            For Estate Agents
          </router-link> -->
          <!--  -->
        </div>
      </q-toolbar-title>
    </q-toolbar>
    <div class="q-pa-sm">
      <div class="copyright-foot width-full">
        <div :class="copywriteClass">
          Copyright © 2021 - 2025
          <a class="text-white" href="/"> {{ whitelabelNameDisplay }} </a>
        </div>
      </div>
    </div>
  </q-footer>
</template>
<script>
import { defineComponent, ref } from 'vue'
export default defineComponent({
  created() {},
  name: 'HtocFooter',
  mounted() {
    setTimeout(() => {
      // footer sometimes loading before rest of page
      // This avoids that
      this.timeToShowFooter = true
      // TODO - investigate if this is affecting pagespeed
      // might want to wrap in a q-no-ssr tag
    }, 500)
  },
  data() {
    return {
      timeToShowFooter: false,
    }
  },
  computed: {
    // configData() {
    //   let configData = currentConfigData()
    //   return configData
    // },
    copywriteClass() {
      if (this.$q.screen.lt.md) {
        return 'text-center'
      } else {
        return 'float-right'
      }
    },
  },
  props: {
    homeUrl: {
      type: String,
      default: 'https://homestocompare.com/',
    },
    serviceEmail: {
      type: String,
      default: '<EMAIL>',
    },
    whitelabelNameDisplay: {
      type: String,
      default: 'HomesToCompare',
    },
  },
})
</script>
