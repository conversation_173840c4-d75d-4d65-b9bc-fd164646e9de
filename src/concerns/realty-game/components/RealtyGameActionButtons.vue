<template>
  <div class="action-buttons q-mt-xl">
    <div class="row q-gutter-md justify-center">
      <!-- Share Results Button -->
      <div class="col-12 col-sm-auto" v-if="showShareButton">
        <q-btn color="primary"
               icon="share"
               :label="shareButtonLabel"
               @click="$emit('share')"
               size="lg"
               class="full-width" />
      </div>

      <!-- View Shareable Results Button -->
      <div class="col-12 col-sm-auto" v-if="showShareableLink">
        <q-btn color="secondary"
               icon="link"
               label="Get Shareable Link"
               @click="$emit('viewShareable')"
               size="lg"
               class="full-width" />
      </div>

      <!-- View Detailed Results Button -->
      <div class="col-12 col-sm-auto" v-if="showDetailedLink">
        <q-btn color="info"
               icon="visibility"
               label="View Full Details"
               @click="$emit('viewDetailed')"
               size="lg"
               class="full-width" />
      </div>

      <!-- Play Again Button -->
      <div class="col-12 col-sm-auto" v-if="showPlayAgain">
        <q-btn color="positive"
               icon="refresh"
               label="Play Again"
               @click="$emit('playAgain')"
               size="lg"
               class="full-width" />
      </div>

      <!-- Start New Game Button -->
      <div class="col-12 col-sm-auto" v-if="showStartNew">
        <q-btn color="accent"
               icon="play_arrow"
               label="Start New Game"
               @click="$emit('startNew')"
               size="lg"
               outline
               class="full-width" />
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  showShareButton: {
    type: Boolean,
    default: true
  },
  showShareableLink: {
    type: Boolean,
    default: false
  },
  showDetailedLink: {
    type: Boolean,
    default: false
  },
  showPlayAgain: {
    type: Boolean,
    default: true
  },
  showStartNew: {
    type: Boolean,
    default: true
  },
  shareButtonLabel: {
    type: String,
    default: 'Share Results'
  }
})

defineEmits(['share', 'viewShareable', 'viewDetailed', 'playAgain', 'startNew'])
</script>

<style scoped>
.action-buttons {
  padding: 2rem 0;
}

@media (max-width: 600px) {
  .action-buttons .q-btn {
    margin-bottom: 0.5rem;
  }
}
</style>
