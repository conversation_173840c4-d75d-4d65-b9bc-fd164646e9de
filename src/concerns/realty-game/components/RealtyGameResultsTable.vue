<template>
  <q-card class="results-card q-mb-lg" flat bordered>
    <q-card-section class="q-pa-lg">
      <div class="text-h6 q-mb-md">
        <q-icon name="leaderboard" color="primary" size="sm" class="q-mr-sm" />
        {{ isCurrentUserSession ? 'How you performed on each property' : `How ${playerName} performed on each property` }}
      </div>
      
      <q-table :rows="gameBreakdown"
               :columns="resultsColumns"
               row-key="uuid"
               flat
               :pagination="{ rowsPerPage: 0 }"
               :hide-bottom="gameBreakdown.length < 10"
               class="results-table">
        
        <template v-slot:body-cell-property="props">
          <q-td :props="props">
            <div class="property-cell">
              <div class="property-address text-weight-medium">
                {{ props.row.listing_details?.address_line_1 }}
              </div>
              <div class="property-location text-caption text-grey-6">
                {{ props.row.listing_details?.address_line_2 }}
              </div>
            </div>
          </q-td>
        </template>

        <template v-if="showPrices" v-slot:body-cell-your_guess="props">
          <q-td :props="props" class="text-center">
            <div class="price-cell">
              {{ formatPrice(props.row.estimated_price_cents, props.row.estimate_currency) }}
            </div>
          </q-td>
        </template>

        <template v-if="showPrices" v-slot:body-cell-actual_price="props">
          <q-td :props="props" class="text-center">
            <div class="price-cell">
              {{ formatPrice(props.row.price_at_time_of_estimate_cents, props.row.estimate_currency) }}
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-difference="props">
          <q-td :props="props" class="text-center">
            <q-chip :color="getDifferenceColor(props.row.difference_percentage)"
                    text-color="white"
                    size="sm">
              {{ props.row.difference_percentage > 0 ? '+' : '' }}{{ Math.round(props.row.difference_percentage) }}%
            </q-chip>
          </q-td>
        </template>

        <template v-slot:body-cell-score="props">
          <q-td :props="props" class="text-center">
            <div class="score-cell">
              <q-circular-progress :value="props.row.score"
                                   :color="getScoreColor(props.row.score)"
                                   size="40px"
                                   :thickness="0.15"
                                   show-value
                                   class="text-weight-bold">
                {{ props.row.score }}
              </q-circular-progress>
            </div>
          </q-td>
        </template>
      </q-table>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  gameBreakdown: {
    type: Array,
    required: true
  },
  showPrices: {
    type: Boolean,
    default: true
  },
  isCurrentUserSession: {
    type: Boolean,
    default: true
  },
  playerName: {
    type: String,
    default: ''
  },
  formatPrice: {
    type: Function,
    required: true
  },
  getScoreColor: {
    type: Function,
    required: true
  }
})

const resultsColumns = computed(() => {
  const columns = [
    {
      name: 'property',
      label: 'Property',
      field: 'property',
      align: 'left',
      style: 'width: 40%',
    }
  ]

  if (props.showPrices && props.isCurrentUserSession) {
    columns.push(
      {
        name: 'your_guess',
        label: 'Your Guess',
        field: 'your_guess',
        align: 'center',
        style: 'width: 20%',
      },
      {
        name: 'actual_price',
        label: 'Actual Price',
        field: 'actual_price',
        align: 'center',
        style: 'width: 20%',
      }
    )
  }

  columns.push(
    {
      name: 'difference',
      label: 'Difference',
      field: 'difference',
      align: 'center',
      style: props.isCurrentUserSession ? 'width: 15%' : 'width: 35%',
    },
    {
      name: 'score',
      label: 'Score',
      field: 'score',
      align: 'center',
      style: props.isCurrentUserSession ? 'width: 15%' : 'width: 35%',
    }
  )

  return columns
})

const getDifferenceColor = (percentage) => {
  const abs = Math.abs(percentage)
  if (abs <= 10) return 'positive'
  if (abs <= 25) return 'primary'
  if (abs <= 50) return 'warning'
  return 'negative'
}
</script>

<style scoped>
.property-cell {
  text-align: left;
}

.property-address {
  font-size: 0.9rem;
}

.property-location {
  font-size: 0.8rem;
}

.price-cell {
  font-weight: 500;
}

.score-cell {
  display: flex;
  justify-content: center;
}

.results-table {
  border-radius: 8px;
}
</style>
