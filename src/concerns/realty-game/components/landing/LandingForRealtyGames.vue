<template>
  <div class="landing-for-realty-games">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <div class="hero-text-content">
          <h1 class="hero-title animate-pop-in">
            The {{ dossierNickname }} Property Price Challenge
          </h1>
          <p class="hero-subtitle animate-fade-in">
            Time to show off your property price knowledge.
          </p>
          <div class="hero-description animate-fade-in">
            <p>Choose from our exciting property price guessing games below and test your market knowledge!</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Games Section -->
    <section class="games-section">
      <div class="games-container">
        <ScootGamesSummary :available-realty-games="availableGamesDetails"
                           :subdomain-name="subdomainName"
                           :auto-load="false"
                           @game-selected="handleGameSelected"
                           @games-loaded="handleGamesLoaded" />
      </div>
    </section>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue';
import { useQuasar } from 'quasar';
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import ScootGamesSummary from 'src/concerns/psq/components/ScootGamesSummary.vue'

export default defineComponent({
  name: 'LandingForRealtyGames',
  components: {
    ScootGamesSummary
  },
  props: {
    availableGamesDetails: {
      type: Array,
      default: () => []
    },
  },
  setup() {
    const $q = useQuasar();

    // Computed properties
    const dossierNickname = computed(() => {
      const subdomainName = pwbFlexConfig.subdomainName;
      return "@" + subdomainName.charAt(0).toUpperCase() + subdomainName.slice(1);
    });

    const subdomainName = computed(() => {
      return pwbFlexConfig.subdomainName;
    });

    // Methods
    const handleGameSelected = (game) => {
      $q.notify({
        type: 'positive',
        message: `Starting ${game.game_title}!`,
        position: 'top'
      });
    };

    const handleGamesLoaded = (games) => {
      console.log('Games loaded:', games);
    };

    return {
      dossierNickname,
      subdomainName,
      handleGameSelected,
      handleGamesLoaded,
    };
  }
});
</script>

<style scoped>
.landing-for-realty-games {
  width: 100%;
  min-height: 100vh;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 80px 20px 60px;
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero-text-content {
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 24px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 400;
  margin: 0 0 24px 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-description {
  font-size: 1.1rem;
  opacity: 0.8;
  max-width: 600px;
  margin: 0 auto;
}

.hero-description p {
  margin: 0;
  line-height: 1.6;
}

/* Games Section */
.games-section {
  background: #f8f9fa;
  padding: 80px 0;
  min-height: 40vh;
}

.games-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Animations */
.animate-pop-in {
  animation: pop-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
  animation-fill-mode: forwards;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    min-height: 50vh;
    padding: 60px 16px 40px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.3rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .games-section {
    padding: 60px 0;
  }

  .games-container {
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-section {
    padding: 40px 12px 30px;
  }

  .games-section {
    padding: 40px 0;
  }
}
</style>