<template>
  <q-card class="summary-card q-mb-lg" flat bordered>
    <q-card-section class="q-pa-lg">
      <div class="text-h6 q-mb-md">
        <q-icon name="analytics" color="primary" size="sm" class="q-mr-sm" />
        {{ isCurrentUserSession ? 'Your Performance Summary' : `${playerName}'s Performance Summary` }}
      </div>
      
      <div class="row q-gutter-md">
        <div class="col-12 col-md-4">
          <div class="stat-item">
            <div class="text-h5 text-weight-bold text-primary">
              {{ playerResults.total_score }}
            </div>
            <div class="text-caption text-grey-7">Total Score</div>
          </div>
        </div>
        
        <div class="col-12 col-md-4">
          <div class="stat-item">
            <div class="text-h5 text-weight-bold text-secondary">
              {{ Math.round(playerResults.average_accuracy_percentage || 0) }}%
            </div>
            <div class="text-caption text-grey-7">Average Accuracy</div>
          </div>
        </div>
        
        <div class="col-12 col-md-4">
          <div class="stat-item">
            <div class="text-h5 text-weight-bold text-info">
              {{ gameBreakdown.length }}
            </div>
            <div class="text-caption text-grey-7">Properties Guessed</div>
          </div>
        </div>
      </div>
      
      <div v-if="comparisonSummary.length > 0" class="q-mt-lg">
        <div class="text-subtitle2 q-mb-sm">Performance Breakdown:</div>
        <div class="row q-gutter-sm">
          <div v-for="summary in comparisonSummary" 
               :key="summary.range" 
               class="col-auto">
            <q-chip :color="getAccuracyColor(summary.accuracy_percentage)" 
                    text-color="white" 
                    size="sm">
              {{ summary.range }}: {{ Math.round(summary.accuracy_percentage) }}%
            </q-chip>
          </div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
const props = defineProps({
  playerResults: {
    type: Object,
    required: true
  },
  gameBreakdown: {
    type: Array,
    required: true
  },
  comparisonSummary: {
    type: Array,
    default: () => []
  },
  isCurrentUserSession: {
    type: Boolean,
    default: true
  },
  playerName: {
    type: String,
    default: ''
  }
})

const getAccuracyColor = (percentage) => {
  if (percentage >= 80) return 'positive'
  if (percentage >= 60) return 'primary'
  if (percentage >= 40) return 'warning'
  return 'negative'
}
</script>

<style scoped>
.stat-item {
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.02);
}

.summary-card {
  border-radius: 12px;
}
</style>
