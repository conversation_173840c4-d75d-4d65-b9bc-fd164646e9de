<template>
  <div class="performance-badge q-mb-md text-center">
    <q-icon :name="playerResults.performance_rating?.icon"
            :color="playerResults.performance_rating?.color"
            size="4em" />
    <div class="text-h4 text-weight-bold q-mt-md"
         :class="`text-${playerResults.performance_rating?.color}`">
      {{ playerResults.performance_rating?.rating }}
    </div>
    <div class="text-h6 text-grey-7">
      {{ playerResults.total_score }} /
      {{ playerResults.max_possible_score }} points
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  playerResults: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.performance-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
