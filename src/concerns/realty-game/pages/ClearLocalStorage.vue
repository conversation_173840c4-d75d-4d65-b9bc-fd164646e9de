<template>
  <div class="q-pa-md">
    <q-no-ssr>
      <div class="row items-center justify-between q-pa-lg">
        <div class="header-left">
          <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">
            <router-link :to="{
              name: 'rPriceGameStartDefault',
              params: {},
            }">
              To Game
            </router-link>
          </h1>
          <p class="text-body1 text-grey-7">
          </p>
        </div>
      </div>
      <q-card class="my-card">
        <q-card-section>
          <div class="text-h6">Local Storage Management</div>
        </q-card-section>

        <q-card-section class="q-gutter-md">
          <q-btn color="negative"
                 label="Clear Local Storage"
                 @click="clearStorage"
                 :disable="isClearing"
                 icon="delete" />
          <div class="q-mt-md">
            <!-- <qrcode-vue :value="currentUrl" :size="200" level="H" /> -->
            <div class="text-caption q-mt-sm">Scan to visit this page</div>
          </div>
        </q-card-section>
      </q-card>
    </q-no-ssr>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useQuasar } from 'quasar';
// import QrcodeVue from 'qrcode.vue'

export default {
  name: 'ClearStorage',
  components: {
    // QrcodeVue
  },
  setup() {
    const $q = useQuasar()
    const isClearing = ref(false)
    // const currentUrl = window.location.href

    // const clearStorage = () => {
    //   isClearing.value = true
    //   try {
    //     localStorage.clear()
    //     // Optional: Notify user of success
    //     // You can use Quasar's notify plugin here if desired
    //   } catch (error) {
    //     console.error('Error clearing local storage:', error)
    //     // Optional: Notify user of error
    //   } finally {
    //     isClearing.value = false
    //   }
    // }

    const clearStorage = () => {
      isClearing.value = true
      try {
        localStorage.clear()
        // ✅ Notify user of success
        $q.notify({
          type: 'positive',
          message: 'Local storage cleared successfully!',
          position: 'top'
        })
      } catch (error) {
        console.error('Error clearing local storage:', error)
        // ❌ Notify user of error
        $q.notify({
          type: 'negative',
          message: 'Failed to clear local storage.',
          position: 'top'
        })
      } finally {
        isClearing.value = false
      }
    }

    return {
      isClearing,
      clearStorage,
      // currentUrl
    }
  }
}
</script>

<style scoped>
.my-card {
  max-width: 400px;
  margin: 0 auto;
}
</style>