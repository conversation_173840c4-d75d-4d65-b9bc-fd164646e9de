<template>
  <div class="realty-game-results-summary">
    <div class="container q-pa-md">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner-dots color="primary"
                        size="3em" />
        <div class="text-h6 q-mt-md">Loading your results...</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="text-h6 q-mt-md text-negative">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults"
               class="q-mt-md" />
      </div>

      <!-- Results Content -->
      <div v-else-if="results"
           class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <RealtyGamePerformanceBadge :player-results="playerResults" />

          <h1 class="text-h4 text-weight-bold text-primary q-mb-lg">
            {{ isCurrentUserSession ? `Well done ${ssGameSession.game_player_nickname}, challenge complete!` :
              `${ssGameSession.game_player_nickname} completed the challenge!` }}
          </h1>

          <div class="text-h6 text-grey-7 q-mb-lg">
            {{ ssGameSession.game_title || 'Property Price Challenge' }}
          </div>
        </div>

        <!-- Brief Summary Card -->
        <!-- <q-card class="summary-card q-mb-xl"
                flat
                bordered>
          <q-card-section class="q-pa-lg text-center">
            <div class="text-h5 q-mb-md">🎉 Challenge Complete!</div>
            <div class="text-body1 q-mb-lg">
              {{ isCurrentUserSession ? 'You' : ssGameSession.game_player_nickname }}
              scored <strong>{{ playerResults.total_score }}/{{ playerResults.max_possible_score }}</strong>
              points with an average accuracy of
              <strong>{{ Math.round(playerResults.average_accuracy_percentage || 0) }}%</strong>
              across {{ gameBreakdown.length }} properties.
            </div>

            <div class="performance-message q-mb-lg">
              <q-icon :name="playerResults.performance_rating?.icon"
                      :color="playerResults.performance_rating?.color"
                      size="2em"
                      class="q-mr-sm" />
              <span class="text-h6"
                    :class="`text-${playerResults.performance_rating?.color}`">
                {{ playerResults.performance_rating?.rating }}
              </span>
            </div>
          </q-card-section>
        </q-card> -->

        <!-- Sharing Encouragement -->
        <q-card class="sharing-card q-mb-xl"
                flat
                bordered>
          <q-card-section class="q-pa-lg text-center">
            <div class="text-h6 q-mb-md">
              <q-icon name="share"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              Share Your Results
            </div>
            <div class="text-body1 q-mb-lg">
              <!-- Challenge your friends and see how they compare!
              Share your results or get a link without revealing the actual property prices. -->
            </div>

            <div class="sharing-options row q-gutter-md justify-center">
              <SocialSharing socialSharingPrompt="Challenge your friends and see how they compare!
              Share your results without revealing the actual property prices."
                             socialSharingTitle="Check out how I did in this property price game"
                             :urlProp="shareableResultsUrl"></SocialSharing>

              <div>
                <a :href="shareableResultsUrl">{{ shareableResultsUrl }}</a>
              </div>
              <!-- <div class="col-12 col-sm-auto">
                <q-btn color="primary"
                       icon="share"
                       label="Share Results"
                       @click="shareResults"
                       size="lg" />
              </div>
              <div class="col-12 col-sm-auto">
                <q-btn color="secondary"
                       icon="link"
                       label="Get Shareable Link"
                       @click="goToShareablePage"
                       size="lg"
                       outline />
              </div> -->
            </div>
          </q-card-section>
        </q-card>

        <!-- Quick Links -->
        <q-card class="links-card q-mb-xl"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 q-mb-md text-center">
              <q-icon name="visibility"
                      color="info"
                      size="sm"
                      class="q-mr-sm" />
              More Details On Your Result
            </div>
            <div class="text-center">
              <q-btn color="info"
                     icon="visibility"
                     label="View Full Results with Prices"
                     @click="goToDetailedPage"
                     size="lg"
                     class="q-mb-md" />
            </div>
            <div class="text-caption text-grey-6 text-center">
              See all property prices and detailed breakdown
            </div>
          </q-card-section>
        </q-card>

        <!-- Action Buttons -->
        <!-- <RealtyGameActionButtons 
          :show-shareable-link="false"
          :show-detailed-link="false"
          @share="shareResults"
          @play-again="startNewGame"
          @start-new="startNewGame" /> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import { useServerRealtyGameResults } from '../../composables/useServerRealtyGameResults'
import RealtyGamePerformanceBadge from '../../components/RealtyGamePerformanceBadge.vue'
// import RealtyGameActionButtons from '../components/RealtyGameActionButtons.vue'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'

const props = defineProps({
  gameSessionId: {
    type: String,
    required: true,
  },
  gameCommunitiesDetails: {
    type: Object,
  },
  shareableResultsUrl: {
    type: String,
    required: true,
  },
  isCurrentUserSession: {
    type: Boolean,
    required: true,
  },
})

const $router = useRouter()
const $route = useRoute()
const $q = useQuasar()

const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
} = useServerRealtyGameResults()

// Computed properties (shareableResultsUrl and isCurrentUserSession now come as props)


// Methods
const loadResults = async () => {
  await fetchResults(
    props.gameSessionId,
    $router.currentRoute.value.params.gameSlug
  )
}

const shareResults = () => {
  const url = window.location.href
  const text = `I just completed the Property Price Challenge! Score: ${playerResults.value.total_score}/${playerResults.value.max_possible_score} (${playerResults.value.performance_rating.rating})`

  if (navigator.share) {
    navigator.share({
      title: 'Property Price Challenge Results',
      text: text,
      url: url
    }).catch(() => {
      fallbackShare(url, text)
    })
  } else {
    fallbackShare(url, text)
  }
}

const fallbackShare = (url, text) => {
  navigator.clipboard.writeText(`${text}\n${url}`).then(() => {
    $q.notify({
      type: 'positive',
      message: 'Results copied to clipboard!',
      position: 'top'
    })
  }).catch(() => {
    $q.notify({
      type: 'info',
      message: 'Share this link: ' + url,
      position: 'top',
      timeout: 5000
    })
  })
}

// const goToShareablePage = () => {
//   $router.push({
//     name: 'rOneOffGameResultsShareable',
//     params: {
//       gameSessionId: props.gameSessionId,
//       gameSlug: $route.params.gameSlug
//     }
//   })
// }

const goToDetailedPage = () => {
  $router.push({
    name: 'rOneOffGameResultsDetailed',
    params: {
      gameSessionId: props.gameSessionId,
      gameSlug: $route.params.gameSlug
    }
  })
}

const startNewGame = () => {
  $router.push({
    name: 'rOneOffGameStart',
    params: { gameSlug: $route.params.gameSlug }
  })
}

// Lifecycle
onMounted(() => {
  loadResults()
})
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
}

.summary-card,
.sharing-card,
.links-card {
  border-radius: 12px;
}

.performance-message {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sharing-options .q-btn {
  min-width: 180px;
}

@media (max-width: 600px) {
  .sharing-options .q-btn {
    width: 100%;
  }
}
</style>
