<template>
  <div class="realty-game-results-detailed">
    <div class="container q-pa-md">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-container text-center q-pa-xl">
        <q-spinner-dots color="primary" size="3em" />
        <div class="text-h6 q-mt-md">Loading detailed results...</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container text-center q-pa-xl">
        <q-icon name="error" color="negative" size="3em" />
        <div class="text-h6 q-mt-md text-negative">{{ error }}</div>
        <q-btn color="primary" 
               label="Try Again" 
               @click="loadResults" 
               class="q-mt-md" />
      </div>

      <!-- Results Content -->
      <div v-else-if="results" class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <RealtyGamePerformanceBadge :player-results="playerResults" />
          
          <h1 class="text-h4 text-weight-bold text-primary q-mb-lg">
            {{ isCurrentUserSession ? `Well done ${ssGameSession.game_player_nickname}, challenge complete` :
               `${ssGameSession.game_player_nickname}'s detailed results` }}
          </h1>
          
          <div class="text-h6 text-grey-7 q-mb-lg">
            {{ ssGameSession.game_title || 'Property Price Challenge' }}
          </div>

          <!-- Privacy Warning -->
          <q-banner v-if="!isCurrentUserSession" class="bg-warning text-dark q-mb-lg" rounded>
            <template v-slot:avatar>
              <q-icon name="warning" />
            </template>
            This page contains sensitive pricing information. Please respect the privacy of this data.
          </q-banner>
        </div>

        <!-- Performance Summary -->
        <RealtyGameSummaryCard 
          :player-results="playerResults"
          :game-breakdown="gameBreakdown"
          :comparison-summary="comparisonSummary"
          :is-current-user-session="isCurrentUserSession"
          :player-name="ssGameSession.game_player_nickname" />

        <!-- Currency Selection -->
        <!-- <q-card v-if="isCurrentUserSession" class="currency-card q-mb-lg" flat bordered>
          <q-card-section class="q-pa-md">
            <div class="row items-center q-gutter-md">
              <div class="col-auto">
                <q-icon name="currency_exchange" color="primary" size="sm" />
                <span class="text-subtitle2 q-ml-sm">Display Currency:</span>
              </div>
              <div class="col-auto">
                <q-select v-model="selectedCurrency"
                          :options="currencyOptions"
                          emit-value
                          map-options
                          dense
                          outlined
                          @update:model-value="onCurrencyChange" />
              </div>
              <div class="col-auto text-caption text-grey-6">
                Original prices shown in {{ gameBreakdown[0]?.estimate_currency || 'GBP' }}
              </div>
            </div>
          </q-card-section>
        </q-card> -->

        <!-- Detailed Results Table (with prices) -->
        <RealtyGameResultsTable 
          :game-breakdown="gameBreakdown"
          :show-prices="true"
          :is-current-user-session="isCurrentUserSession"
          :player-name="ssGameSession.game_player_nickname"
          :format-price="formatPrice"
          :get-score-color="getScoreColor" />

        <!-- Sharing Options -->
        <!-- <q-card v-if="isCurrentUserSession" class="sharing-options-card q-mb-xl" flat bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 q-mb-md text-center">
              <q-icon name="share" color="primary" size="sm" class="q-mr-sm" />
              Sharing Options
            </div>
            
            <div class="row q-gutter-md">
              <div class="col-12 col-md-6">
                <q-card class="sharing-option" flat bordered>
                  <q-card-section class="text-center">
                    <q-icon name="share" color="primary" size="2em" class="q-mb-md" />
                    <div class="text-subtitle1 q-mb-sm">Share Summary</div>
                    <div class="text-caption text-grey-6 q-mb-md">
                      Share your basic results and performance rating
                    </div>
                    <q-btn color="primary" 
                           label="Share Summary" 
                           @click="goToSummaryPage" 
                           size="sm" />
                  </q-card-section>
                </q-card>
              </div>
              
              <div class="col-12 col-md-6">
                <q-card class="sharing-option" flat bordered>
                  <q-card-section class="text-center">
                    <q-icon name="link" color="secondary" size="2em" class="q-mb-md" />
                    <div class="text-subtitle1 q-mb-sm">Shareable Link</div>
                    <div class="text-caption text-grey-6 q-mb-md">
                      Share detailed results without revealing prices
                    </div>
                    <q-btn color="secondary" 
                           label="Get Link" 
                           @click="goToShareablePage" 
                           size="sm" />
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </q-card-section>
        </q-card> -->

        <!-- Action Buttons -->
        <!-- <RealtyGameActionButtons 
          :show-shareable-link="isCurrentUserSession"
          :show-detailed-link="false"
          @share="shareResults"
          @view-shareable="goToShareablePage"
          @play-again="startNewGame"
          @start-new="startNewGame" /> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import { useServerRealtyGameResults } from '../composables/useServerRealtyGameResults'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'
import RealtyGamePerformanceBadge from '../components/RealtyGamePerformanceBadge.vue'
import RealtyGameSummaryCard from '../components/RealtyGameSummaryCard.vue'
import RealtyGameResultsTable from '../components/RealtyGameResultsTable.vue'
// import RealtyGameActionButtons from '../components/RealtyGameActionButtons.vue'

const props = defineProps({
  gameSessionId: {
    type: String,
    required: true,
  },
  gameCommunitiesDetails: {
    type: Object,
  },
})

const $router = useRouter()
const $route = useRoute()
const $q = useQuasar()

const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
} = useServerRealtyGameResults()

const { getCurrentSessionId, getCurrencySelection, saveCurrencySelection } = useRealtyGameStorage()
const { formatPrice, currencyOptions } = useCurrencyConverter()

// Local state
const selectedCurrency = ref('GBP')

// Computed properties
const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === props.gameSessionId
})

// Methods
const loadResults = async () => {
  await fetchResults(
    props.gameSessionId,
    $router.currentRoute.value.params.gameSlug
  )
  
  // Load saved currency preference
  if (isCurrentUserSession.value) {
    selectedCurrency.value = getCurrencySelection(props.gameSessionId)
  }
}

const onCurrencyChange = (newCurrency) => {
  selectedCurrency.value = newCurrency
  if (isCurrentUserSession.value) {
    saveCurrencySelection(newCurrency, props.gameSessionId)
  }
}

const shareResults = () => {
  const url = window.location.href
  const text = `${isCurrentUserSession.value ? 'I' : ssGameSession.value.game_player_nickname} just completed the Property Price Challenge! Score: ${playerResults.value.total_score}/${playerResults.value.max_possible_score} (${playerResults.value.performance_rating.rating})`

  if (navigator.share) {
    navigator.share({
      title: 'Property Price Challenge Results',
      text: text,
      url: url
    }).catch(() => {
      fallbackShare(url, text)
    })
  } else {
    fallbackShare(url, text)
  }
}

const fallbackShare = (url, text) => {
  navigator.clipboard.writeText(`${text}\n${url}`).then(() => {
    $q.notify({
      type: 'positive',
      message: 'Results copied to clipboard!',
      position: 'top'
    })
  }).catch(() => {
    $q.notify({
      type: 'info',
      message: 'Share this link: ' + url,
      position: 'top',
      timeout: 5000
    })
  })
}

const goToSummaryPage = () => {
  $router.push({
    name: 'rPriceGameResults',
    params: { 
      gameSessionId: props.gameSessionId,
      gameSlug: $route.params.gameSlug
    }
  })
}

const goToShareablePage = () => {
  $router.push({
    name: 'rPriceGameResultsShareable',
    params: { 
      gameSessionId: props.gameSessionId,
      gameSlug: $route.params.gameSlug
    }
  })
}

const startNewGame = () => {
  $router.push({ name: 'rPriceGuessStart' })
}

// Lifecycle
onMounted(() => {
  loadResults()
})
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
}

.currency-card,
.sharing-options-card {
  border-radius: 12px;
}

.sharing-option {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.sharing-option:hover {
  transform: translateY(-2px);
}
</style>
