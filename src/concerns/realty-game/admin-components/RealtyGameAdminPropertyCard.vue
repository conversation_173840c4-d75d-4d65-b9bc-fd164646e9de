<template>
  <q-card :class="['property-card q-mb-md', { 'property-hidden': !property.visible }]">
    <q-card-section>
      <div class="row items-start justify-between">
        <div class="col-8">
          <div class="property-header q-mb-sm">
            <q-badge :color="property.visible ? 'positive' : 'negative'"
                     :label="property.visible ? 'Visible' : 'Hidden'"
                     class="q-mr-sm" />
            <span class="text-caption text-grey-6">
              Property {{ index + 1 }}
            </span>
          </div>

          <!-- Editable Title -->
          <div class="property-title q-mb-sm">
            <q-input v-model="localTitle"
                     label="Property Title"
                     outlined
                     dense
                     @blur="onTitleBlur"
                     :loading="property._updating" />
          </div>

          <div v-if="property.listing_display_url"
               class="text-subtitle1 text-weight-medium">
            <a :href="property.listing_display_url">{{ property.listing_display_url }}</a>
          </div>

          <div class="property-details text-caption text-grey-6">
            <div>{{ property.formatted_display_price }}</div>
            <div>{{ property.street_address }}, {{ property.city }}</div>
            <div>{{ property.count_bedrooms }} bed, {{ property.count_bathrooms }} bath</div>
          </div>
        </div>

        <div class="col-4 text-right">
          <!-- Property Visibility Toggle -->
          <q-toggle v-model="localVisible"
                    color="positive"
                    size="lg"
                    @update:model-value="onVisibilityToggle"
                    :loading="property._updating" />
        </div>
      </div>
    </q-card-section>

    <!-- Property Images -->
    <q-card-section v-if="property.sale_listing_pics?.length > 0">
      <div class="images-header q-mb-sm">
        <h6 class="q-my-none">Property Images ({{ property.sale_listing_pics.length }})</h6>
        <p class="text-caption text-grey-6 q-mb-none">
          Toggle individual image visibility
        </p>
      </div>

      <div class="images-grid">
        <div v-for="(image, imgIndex) in property.sale_listing_pics"
             :key="image.uuid"
             class="image-item"
             :class="{ 'image-hidden': image.flag_is_hidden }">
          <div class="image-container">
            <img :src="image.image_details?.small_fit?.url || image.photo_slug"
                 :alt="image.photo_title"
                 class="property-image" />
            <div class="image-overlay">
              <q-toggle :model-value="!image.flag_is_hidden"
                        @update:model-value="onImageVisibilityToggle(image, !$event)"
                        color="white"
                        size="sm"
                        :loading="image._updating" />
            </div>
          </div>
          <div class="image-info q-mt-xs">
            <div class="text-caption font-weight-medium">
              {{ image.photo_title || `Image ${imgIndex + 1}` }}
            </div>
            <div class="text-caption text-grey-6">
              {{ image.flag_is_hidden ? 'Hidden' : 'Visible' }}
            </div>
          </div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, watch } from 'vue'
const props = defineProps({
  property: { type: Object, required: true },
  index: { type: Number, required: true }
})
const emit = defineEmits([
  'update-title',
  'update-visibility',
  'update-image-visibility'
])

const localTitle = ref(props.property.title)
const localVisible = ref(props.property.visible)

watch(() => props.property.title, (newVal) => {
  localTitle.value = newVal
})
watch(() => props.property.visible, (newVal) => {
  localVisible.value = newVal
})

function onTitleBlur() {
  if (localTitle.value !== props.property.title) {
    emit('update-title', localTitle.value)
  }
}
function onVisibilityToggle(val) {
  emit('update-visibility', val)
}
function onImageVisibilityToggle(image, isHidden) {
  emit('update-image-visibility', image, isHidden)
}
</script>

<style scoped>
.property-card {
  transition: opacity 0.3s ease;
}
.property-hidden {
  opacity: 0.6;
}
.property-header {
  display: flex;
  align-items: center;
}
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}
.image-item {
  transition: opacity 0.3s ease;
}
.image-hidden {
  opacity: 0.5;
}
.image-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.property-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}
.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px;
}
.image-info {
  text-align: center;
}
</style> 