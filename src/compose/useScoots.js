import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  function getScootForRealtyGames(subdomainName) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/show_games/${subdomainName}`
    return axios.get(apiUrl, {})
  }

  function getScoot(subdomainName) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/show/${subdomainName}`
    return axios.get(apiUrl, {})
  }

  function checkScoot(subdomainName, accessCode) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/check/${subdomainName}/${accessCode}`
    return axios.post(apiUrl, {})
  }

  function getManagementGames(subdomainName) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_games_mgmt/show_available_games/${subdomainName}`
    return axios.get(apiUrl, {})
  }

  function updateScootData(subdomainName, scootData) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/update/${subdomainName}`
    return axios.put(apiUrl, { scoot: scootData })
  }

  function addGameToScoot(subdomainName, gameUuid) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/add_game/${subdomainName}`
    return axios.post(apiUrl, { game_uuid: gameUuid })
  }

  function removeGameFromScoot(subdomainName, gameUuid) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/remove_game/${subdomainName}`
    return axios.delete(apiUrl, { data: { game_uuid: gameUuid } })
  }

  return {
    getScoot,
    getScootForRealtyGames,
    checkScoot,
    getManagementGames,
    updateScootData,
    addGameToScoot,
    removeGameFromScoot,
  }
}
