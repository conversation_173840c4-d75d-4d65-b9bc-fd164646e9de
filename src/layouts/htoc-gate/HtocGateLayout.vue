<template>
  <q-layout view="lhh LpR ffr" class="htoc-gate-layout-outer">
    <!-- <HtocGateMarketingHeader></HtocGateMarketingHeader> -->
    <q-header class="htoc-gate-mht-ctr bg-white" reveal elevated>
      <q-toolbar
        style=""
        class="htoc-gate-marketing-header-toolbar container max-ctr"
      >
        <q-toolbar-title class="inline-flex items-center">
          <div class="toolbar-site-label-main">
            <a class="ignore-link" href="https://homestocompare.com/">
              <div>
                <span class="color-second">HOMES</span>
                <span style="color: black">TO</span>
                <span class="color-first">COMPARE</span>
              </div>
            </a>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container
      :class="[`main-layout-htoc-2024g-gate`, maxCtrClass]"
      style="padding-top: 50px"
    >
      <!-- must be careful not to add :key="$route.fullPath" below!! -->
      <router-view
        @blurbCta="blurbCta"
        @showNotification="showNotification"
        :whitelabelNameDisplay="whitelabelNameDisplay"
        :serviceEmail="serviceEmail"
      />
    </q-page-container>
    <div class="ajax-bar-container">
      <q-ajax-bar
        ref="bar"
        position="top"
        color="accent"
        size="10px"
        :skip-hijack="false"
        :hijack-filter="loadingBarFilterFn"
      />
    </div>
    <HtocFooter
      :whitelabelNameDisplay="whitelabelNameDisplay"
      :serviceEmail="serviceEmail"
    ></HtocFooter>
    <!-- <HtocMobileFooter v-if="showMobileFooter"></HtocMobileFooter>
    <HtocFooter v-else></HtocFooter>
    <NewAccountEnquiryPrompt :showNewAccEnqPrompt="showNewAccEnqPrompt"
                             :selectedAccountPlan="selectedAccountPlan"
                             @blurbCtaEnd="blurbCtaEnd"></NewAccountEnquiryPrompt> -->
  </q-layout>
</template>
<script>
// import NewAccountEnquiryPrompt from "src/components/customer/NewAccountEnquiryPrompt.vue"
import HtocFooter from 'src/concerns/dossiers/components/head-foot/HtocFooter.vue'
// import HtocMobileFooter from "src/apps/htoc-gate/components/head-foot/HtocMobileFooter.vue"
import { defineComponent, ref } from 'vue'
import { useQuasar } from 'quasar'

export default defineComponent({
  name: 'HtocGateLayout',
  // inject: ["currentUserProvider"],
  components: {
    // // HtocGateMarketingHeader,
    HtocFooter,
    // HtocMobileFooter,
    // NewAccountEnquiryPrompt,
  },
  data() {
    return {
      showNewAccEnqPrompt: false,
      selectedAccountPlan: 'free',
    }
  },
  props: {
    serviceEmail: {
      type: String,
      default: '<EMAIL>',
    },
    whitelabelNameDisplay: {
      type: String,
      default: 'HomesToCompare',
    },
  },
  computed: {
    showMobileFooter() {
      return this.$q.platform.is.mobile
    },
    maxCtrClass() {
      if (this.$route.name === 'rSubdomainRoot') {
        return ''
      } else {
        return 'max-ctr'
      }
    },
  },
  methods: {
    blurbCta(selectedAccountPlan) {
      this.selectedAccountPlan = selectedAccountPlan
      this.showNewAccEnqPrompt = true
    },
    blurbCtaEnd() {
      this.showNewAccEnqPrompt = false
    },
  },
  setup() {
    const $q = useQuasar()
    function showNotification(notificationMessage) {
      $q.notify(notificationMessage)
    }
    return {
      showNotification,
      loadingBarFilterFn(url) {
        return !(url.includes('prop_ev_init') || !url.includes('user'))
        // example (only https://my-service.com/* should trigger)
        // return /^https:\/\/my-service\.com/.test(url)
      },
      // qLang: $q.lang,
    }
  },
  // preFetch({
  //   store,
  //   currentRoute,
  //   previousRoute,
  //   redirect,
  //   ssrContext,
  //   urlPath,
  //   publicPath,
  // }) {
  // },
  // mounted: function () { },
  // watch: {
  //   "currentUserProvider.state.currentUser": {
  //     handler(newCurrentUser, oldVal) {
})
</script>
<style>
.main-layout-htoc-gate-2024g {
  /* fix the edit tab disappearing */
  padding-top: 50px;
}
</style>
