<template>
  <q-layout
    view="hHh lpR fFf"
    class="max-ctr h2c-gpt-dash-layout"
    style="height: 100vh"
  >
    <!-- <SppTopAdminSection></SppTopAdminSection> -->
    <q-header class="bg-white h2c-sbd-dash-head" reveal elevated>
      <q-toolbar style="" class="marketing-header-toolbar container max-ctr">
        <q-btn
          color="black"
          flat
          dense
          round
          @click="toggleLeftDrawer"
          icon="menu"
          aria-label="Menu"
        />
        <q-toolbar-title class="inline-flex items-center">
          <div class="toolbar-site-label-main flex" style="display: flex">
            <a class="ignore-link flex" href="https://homestocompare.com/">
              <div style="display: flex" class="q-mr-md">
                <span class="color-second">HOMES</span>
                <span style="color: black">TO</span>
                <span class="color-first">COMPARE</span>
              </div>
            </a>
            <router-link
              class="ignore-link flex"
              :to="{ name: 'rAdminRootIndex' }"
            >
              <div style="color: black">/&nbsp;&nbsp;</div>
              <div
                style="
                  display: flex;
                  text-transform: uppercase;
                  font-size: large;
                  padding-top: 2px;
                "
                class="q-mr-md"
              >
                <span class="color-second">{{ subdomainLabel }}</span>
              </div>
            </router-link>
            <router-link class="ignore-link flex" :to="{ name: 'rAdminRoot' }">
              <div style="color: black">/&nbsp;&nbsp;</div>
              <div
                style="
                  display: flex;
                  text-transform: uppercase;
                  font-size: large;
                  padding-top: 2px;
                "
                class="q-mr-md"
              >
                <span class="color-second">Dashboard</span>
              </div>
            </router-link>
          </div>
        </q-toolbar-title>
        <!--  -->
      </q-toolbar>
    </q-header>
    <div class="max-ctr max-q-dr-ctr h2c-sbd-dash-drw" style="">
      <!-- spt 2023 - previously had margin-top: -50px above to fix gap above drawer -->
      <q-drawer
        v-model="leftDrawerOpen"
        fix
        show-if-above
        bordered
        class="bg-grey text-white h2c-gpt-drawer"
        style="height: 100vh"
      >
        <!-- <q-page-sticky position="left" :offset="[18, 0]">
          <q-btn round color="accent" icon="arrow_back" />
        </q-page-sticky> -->
        <q-img
          class="absolute-top"
          src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="
          style="height: 160px"
        >
          <div class="absolute-full flex flex-center text-white">
            <div class="bg-transparent">
              <router-link style="font-size: medium" :to="dashRootRoute">
                <q-avatar
                  text-color="white"
                  color="accent"
                  size="56px"
                  class="q-mb-sm"
                >
                  G
                </q-avatar>
                <div class="text-weight-bold text-white">Your Dashboard</div>
              </router-link>
              <div v-if="weHaveAValidSignedInUser">
                {{ currentSbdUserProvider.state.currentBeUser.email }}
                <div class="htoc-sign-out-ctr text-white" @click="startSignOut">
                  <div style="font-size: small">
                    <span class="text-white"> (Sign Out) </span>
                  </div>
                </div>
              </div>
              <div v-else>
                @guest_user
                <div class="htoc-sign-in-ctr text-white">
                  <!-- <router-link style="font-size: small" :to="loginRoute">
                    <span class="text-white"> (Sign In) </span>
                  </router-link> -->
                </div>
              </div>
              <!-- <div>
                @guest_user
                <div class="text-white">
                  <router-link style="font-size: small" :to="loginRoute">
                    <span class="text-white"> (Sign In) </span>
                  </router-link>
                </div>
              </div> -->
            </div>
          </div>
        </q-img>
        <q-scroll-area
          class="absolute-top h2c-drawer-scroll-area bg-gray text-white"
          style="height: 100%; margin-top: 160px; border-right: 1px solid #ddd"
        >
          <q-list class="h2c-drawer-inner-list">
            <q-item
              :to="{ name: 'rSubdomainManageLanding' }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Landing Page</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              :to="{ name: 'rMyPurchaseEvaluations' }"
              active-class="q-item-h2c-active"
              :exact="false"
              :class="propertiesRouteClass"
              clickable
              v-ripple
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Properties</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              :to="{ name: 'rMySubdomainEvalComparisonsContainer' }"
              active-class="q-item-h2c-active"
              :exact="false"
              :class="comparisonsRouteClass"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Comparisons</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              :to="{ name: 'rExtraServices' }"
              active-class="q-item-h2c-active"
              :exact="false"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Services</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              v-if="showAllDashTabs"
              :to="{ name: 'rMapsLanding' }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Maps</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              v-if="showAllDashTabs"
              :to="{ name: 'rMyBexAlerts' }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Sell Your Home</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              v-if="showAllDashTabs"
              :to="{ name: 'rChecklists' }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Checklists</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              v-if="showAllDashTabs"
              :to="{ name: 'rCalendar' }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Calendar</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              v-if="showAllDashTabs"
              :to="{ name: 'rSheets' }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Sheets</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              v-if="showAllDashTabs"
              :to="{ name: 'rCollaborate' }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Artificial Intelligence</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              :to="{ name: 'rDashInfoPage', params: { page_slug: 'faqs' } }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>FAQS </q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              :to="{ name: 'rFeedback', params: {} }"
              active-class="q-item-h2c-active"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Feedback</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>
      </q-drawer>
    </div>

    <q-page-container
      :class="[`main-layout-h2c-sbds-dash`, maxCtrClass]"
      style="padding-top: 50px"
    >
      <q-no-ssr>
        <q-scroll-area
          v-if="currentSvAccessToQuest"
          class="main-h2c-dash-scroll"
          :visible="false"
          style="height: 100vh; max-width: 100vw; contain: none"
        >
          <div v-if="hasFullAccessToQuest">
            <div class="main-h2c-dash-scroll-inner" style="">
              <router-view
                :rootDashSvt="currentSbdUserProvider.state.currentSvt"
                :currentAgency="currentAgency"
                :purchaseEvaluations="purchaseEvaluations"
                :key="$route.fullPath"
                @showNotification="showNotification"
              />
            </div>
            <!-- -->
          </div>
          <div v-else>
            <div class="q-pa-lg q-ma-lg text-center">Sorry, access denied</div>
            <!-- <SubdomainAccessBlock> </SubdomainAccessBlock> -->
          </div>
        </q-scroll-area>
      </q-no-ssr>
    </q-page-container>
    <div class="ajax-bar-container">
      <!-- <q-ajax-bar
        ref="bar"
        position="top"
        color="accent"
        size="10px"
        :skip-hijack="false"
        :hijack-filter="loadingBarFilterFn"
      /> -->
    </div>
    <!-- <H2cMobileFooter v-if="showMobileFooter"></H2cMobileFooter> -->
    <!-- <H2cFooter v-else></H2cFooter> -->
  </q-layout>
</template>
<script>
import { defineComponent, ref } from 'vue'
import { useQuasar } from 'quasar'
// import useQuests from "src/compose/useQuests.js"
// import useEditHelper from "src/compose/useEditHelper.js"
// import useAccessManager from "src/compose/useAccessManager.js"
// // import SubdomainAccessBlock from "src/components/auth/SubdomainAccessBlock.vue"
export default defineComponent({
  name: 'H2cSubdomainsDashLayout',
  inject: ['currentSbdUserProvider'],
  components: {
    // SubdomainAccessBlock,
  },
  methods: {
    startSignOut() {
      // this.authStore.logout().then(
      //   (result) => {
      //     // result here is undefined
      //     window.location.href = "/"
      //     // location.reload()
      //   },
      //   (error) => {}
      // )
    },
  },
  computed: {
    weHaveAValidSignedInUser() {
      // had previously been using currentSbdUserProvider.state.currentBeUser.email
      return !!this.currentSbdUserProvider.state.currentFbUser.accessToken
    },
    // subdomainLabel() {
    //   let subdomainLabel = "Demo"
    //   if (process.env.SERVER) {
    //     // console.log(`ssr header host is ${this.ssrContext.req.headers["host"]}`)
    //     subdomainLabel = this.ssrContext.req.headers["host"].split(".")[0] || "Demo"
    //   } else {
    //     subdomainLabel = window.location.host.split(".")[0] || "Demo"
    //   }
    //   // console.log(`Subdm is ${subdomainLabel}`)
    //   return subdomainLabel
    // },
    hasFullAccessToQuest() {
      return true
      // return (
      //   this.currentSvAccessToQuest && this.currentSvAccessToQuest.full_control_on_quest
      // )
    },
    showAllDashTabs() {
      if (this.$route.query.showall && this.$route.query.showall === '1') {
        return true
      } else {
        // Todo: change to false before deploy:
        return false
      }
    },
    propertiesRouteClass() {
      if (this.$route.path.includes('my-properties')) {
        return 'q-item-h2c-active'
      } else {
        return ''
      }
    },
    comparisonsRouteClass() {
      if (this.$route.path.includes('my-comparisons')) {
        return 'q-item-h2c-active'
      } else {
        return ''
      }
    },
    dashRootRoute() {
      return {
        name: 'rAdminRoot',
        params: {
          // dashParam: dashParam,
        },
      }
    },
    loginRoute() {
      return {
        name: 'rPwbProLoginPage',
      }
    },
    maxCtrClass() {
      if (this.$route.name === 'rAdminRootIndex') {
        return ''
      } else {
        return 'max-ctr'
      }
    },
  },
  setup() {
    const $q = useQuasar()
    function showNotification(notificationMessage) {
      $q.notify(notificationMessage)
    }
    const leftDrawerOpen = ref(false)
    // const authStore = useAuthStore()
    // const { getPurchaseEvaluationsForQuest } = useQuests()
    // const { makeEditCall } = useEditHelper()

    // const { checkSiteGuestAccessToQuest } = useAccessManager()
    return {
      // checkSiteGuestAccessToQuest,
      // getPurchaseEvaluationsForQuest,
      // makeEditCall,
      // authStore,
      // $q,
      leftDrawerOpen,
      toggleLeftDrawer() {
        leftDrawerOpen.value = !leftDrawerOpen.value
      },
      // siteConfig,
      showNotification,
    }
  },
  data() {
    return {
      currentSvAccessToQuest: true,
      rootDashSvt: '',
      purchaseEvaluations: [],
      currentAgency: {},
    }
  },
  props: {
    subdomainLabel: {
      type: String,
    },
    // subdomainQuest: {
    //   type: Object,
    //   default() {
    //     return {}
    //   },
    // },
  },
  mounted: function () {
    // I prefer explicitly passing rootDashSvt to child routes rather than picking it up
    // from currentSbdUserProvider
    this.rootDashSvt = this.currentSbdUserProvider.state.currentSvt
  },
  watch: {
    // subdomainQuest: {
    //   immediate: true,
    //   // July 2023: needed above to be sure purchaseEvaluations were
    //   // populated on route changes
    //   handler(newVal, oldVal) {
    //     // Spt starting with currentSvAccessToQuest as null
    //     // and then setting here so nothing gets rendered (         <q-scroll-area
    //     // v-if="currentSvAccessToQuest")
    //     // untill subdomainQuest is returned
    //     // purchaseEvaluations length check below might not be needed
    //     if (newVal && newVal.uuid && this.purchaseEvaluations.length < 1) {
    //       let questUuid = newVal.uuid
    //       let retrievalObject = {
    //         questUuid: questUuid,
    //         // currentSvt: this.currentSbdUserProvider.state.currentSvt,
    //         // Aug 2023 - will now use sgt (site_guest_token)
    //         // instead of svt for signed_in users
    //         currentSgt: this.currentSbdUserProvider.state.currentBeUser.sgt,
    //       }
    //       let helperDetails = {}
    //       this.makeEditCall(
    //         this.getPurchaseEvaluationsForQuest,
    //         retrievalObject,
    //         helperDetails
    //       ).then((responseObject) => {
    //         this.pageIsLoading = false
    //         this.purchaseEvaluations = responseObject.data.purchase_evaluations || []
    //         this.receivedShares = responseObject.data.received_shares || {}
    //       })
    //       // Spt 2023 - surely it will make sense to eventually merge above and below
    //       this.makeEditCall(
    //         this.checkSiteGuestAccessToQuest,
    //         retrievalObject,
    //         helperDetails
    //       ).then((responseObject) => {
    //         // this.hasAccessToQuest = responseObject.data.success
    //         if (responseObject.data.success) {
    //           this.currentSvAccessToQuest = responseObject.data.access
    //         } else {
    //           // spt 2023
    //           // setting below any earlier can result in a flash
    //           // "access denied" showing
    //           this.currentSvAccessToQuest = {}
    //         }
    //       })
    //     }
    //   },
    // },
  },
})
</script>
<style scoped>
.q-item-h2c-active {
  background: lightgray;
  color: #665df5;
}

.main-layout-h2c-dash {
  /* fix the edit tab disappearing */
  padding-top: 50px;
}

.main-h2c-dash-scroll-inner {
  /* Allow room to see full page even when chat gpt toast has expanded */
  margin-bottom: 400px;
}
</style>
