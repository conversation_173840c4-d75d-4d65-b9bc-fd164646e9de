const fs = require('fs');
const path = require('path');

const routesFilePath = path.resolve(__dirname, 'src/apps/hpg-main/router/routes.js');
const baseUrl = 'https://housepriceguess.com';

function extractPathsFromRoutesFile(filePath) {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const collectedPaths = new Set();

    const routeBlockRegex = /\{\s*path:\s*['"](.*?)['"]([\s\S]*?)\}/g;
    const childrenRegex = /children:\s*\[([\s\S]*)\]/;
    const metaRobotsNoIndexRegex = /meta:\s*\{[\s\S]*?robots:\s*['"]noindex,\s*nofollow['"]/;
    const redirectRegex = /redirect:\s*['"]/; // Just checks for presence

    function findRoutesRecursive(content, currentBasePath, parentExcluded = false) {
        let blockMatch;
        const localRouteBlockRegex = new RegExp(routeBlockRegex.source, 'g');

        while ((blockMatch = localRouteBlockRegex.exec(content)) !== null) {
            const routePathSegment = blockMatch[1];
            const context = blockMatch[2];

            let fullPath;
            if (currentBasePath === '/') {
                fullPath = routePathSegment === '' ? '/' : `/${routePathSegment}`;
            } else if (currentBasePath === '') {
                 fullPath = routePathSegment === '' ? '/' : `/${routePathSegment}`;
            } else {
                fullPath = routePathSegment === '' ? currentBasePath : `${currentBasePath}/${routePathSegment}`;
            }
            fullPath = `/${fullPath.replace(/^\/+|\/+$/g, '')}`;
            if (fullPath === '' && routePathSegment === '') fullPath = '/';


            const hasMetaRobotsNoIndex = metaRobotsNoIndexRegex.test(context);
            const hasRedirect = redirectRegex.test(context);
            const isDynamic = routePathSegment.includes(':') || routePathSegment.includes('*');
            const isCatchAll = routePathSegment.includes('(.*)*');

            let currentSegmentExcluded = parentExcluded || isDynamic || isCatchAll || hasRedirect || hasMetaRobotsNoIndex;

            if (!currentSegmentExcluded) {
                collectedPaths.add(fullPath);
                // console.log(`Added path: ${fullPath} (segment: ${routePathSegment}, base: ${currentBasePath})`);
            } else {
                // console.log(`Skipping path: ${fullPath} (segment: ${routePathSegment}, dynamic: ${isDynamic}, redirect: ${hasRedirect}, noindex: ${hasMetaRobotsNoIndex}, parentExcluded: ${parentExcluded})`);
            }

            const childrenMatch = childrenRegex.exec(context);
            if (childrenMatch && childrenMatch[1]) {
                // Pass `currentSegmentExcluded` as `parentExcluded` for children
                findRoutesRecursive(childrenMatch[1], fullPath, currentSegmentExcluded);
            }
        }
    }

    const mainRoutesArrayMatch = /const routes = \[([\s\S]*)\]/m.exec(fileContent);
    if (mainRoutesArrayMatch && mainRoutesArrayMatch[1]) {
        findRoutesRecursive(mainRoutesArrayMatch[1], '', false);
    }

    return [...collectedPaths].sort();
}

try {
    const extractedPaths = extractPathsFromRoutesFile(routesFilePath);
    // console.log("Final Extracted paths (3rd attempt):", extractedPaths);

    const today = new Date().toISOString().split('T')[0];

    const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${extractedPaths
    .map(pagePath => {
        const locPath = pagePath === '/' ? '' : pagePath;
        return `  <url>
    <loc>${baseUrl}${locPath}</loc>
    <lastmod>${today}</lastmod>
  </url>`;
    })
    .join('\n')}
</urlset>`;

    fs.writeFileSync('sitemap.xml', sitemapContent);
    console.log('sitemap.xml generated successfully with further refined logic.');
} catch (error) {
    console.error('Error generating sitemap with further refined logic:', error);
}
